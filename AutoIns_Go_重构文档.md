# AutoIns 自动录单系统 Go 重构文档

## 1. 项目概述

### 1.1 系统简介

AutoIns 是一个多保险公司自动录单集成系统，主要功能是接收投保平台的保单数据，自动转换为各保险公司所需的格式，并完成自动录单。系统支持多种保险公司的接入，包括人保财险、太保财险、平安财险、华泰财险、中华联合、史带财险等。

### 1.2 当前技术栈

- **后端**: PHP 7 + Laravel 5.5
- **前端**: Bootstrap + jQuery
- **数据库**: MySQL
- **队列**: Laravel Queue
- **自动化**: Python 2.7 + Splinter 0.13

### 1.3 重构目标

将现有 PHP/Laravel 系统重构为 Go 语言微服务架构，提升系统性能、可维护性和扩展性。

## 2. 系统架构分析

### 2.1 当前系统架构

#### 2.1.1 核心模块

1. **报文模块**: 保存和处理投保平台投递的保单数据
2. **推送模块**: 保单生效后回调通知投保平台
3. **平台模块**: 管理投保平台，生成接口参数
4. **产品模块**: 管理投保平台对应的产品配置
5. **邮件模块**: 录单失败时发送邮件通知
6. **管理员模块**: 系统用户管理

#### 2.1.2 业务流程图

上面的流程图展示了完整的保单处理流程，从投保平台提交数据到最终回调通知的全过程。

#### 2.1.3 数据流转

系统的核心数据流转过程如下：

1. **数据接收**: 投保平台通过 API 提交保单数据
2. **数据验证**: 验证 JWT 令牌和业务数据完整性
3. **数据转换**: 根据保险公司要求转换数据格式
4. **录单处理**: 调用保险公司接口或自动化脚本
5. **状态更新**: 根据录单结果更新保单状态
6. **通知推送**: 异步推送结果到投保平台

#### 2.1.4 保单状态管理

保单在系统中有明确的状态流转机制，上面的状态图展示了各状态之间的转换关系：

- **未处理(0)**: 报文已创建，等待处理
- **已提交(1)**: 已提交到保险公司，等待结果
- **已完成(2)**: 已获取保单号，处理完成
- **已作废(-1)**: 处理失败，已作废

### 2.2 数据库设计

#### 2.2.1 核心表结构

系统采用关系型数据库设计，上面的 ER 图展示了主要表之间的关系。核心表包括：

- **platforms**: 投保平台管理
- **products**: 保险产品配置
- **messages**: 保单报文主表
- **message_attaches**: 报文详细数据
- **notifies**: 通知推送记录
- **platform_products**: 平台产品关联

详细的数据库表结构定义请参考 `config/database_schema.json` 文件。

#### 2.2.2 数据关系

- 一个平台可以有多个产品配置
- 一个产品可以被多个平台使用
- 每个报文属于一个平台和一个产品
- 每个报文有一个详细数据记录
- 每个报文可以有多个通知记录

## 3. 保险公司适配器分析

### 3.1 适配器架构

系统采用适配器模式来处理不同保险公司的接入需求。每个保险公司都有独立的控制器实现，通过统一的接口进行调用。

#### 3.1.1 适配器分类

**自动化录单类型 (AUTO)**:

- 使用 Python + Splinter 进行网页自动化操作
- 模拟人工录单过程
- 适用于没有 API 接口的保险公司

**API 接口类型 (API)**:

- 直接调用保险公司提供的 API 接口
- 数据格式包括 XML 和 JSON
- 响应速度快，稳定性高

#### 3.1.2 支持的保险公司

详细的保险公司配置信息请参考 `config/insurance_companies_config.json` 文件，包括：

1. **人保财险 (PICC)** - 自动化录单，支持国内外业务
2. **太保财险 (CPIC)** - 自动化录单，复杂的协议配置
3. **平安财险 (PINGAN)** - 自动化录单，支持跨境电商
4. **华泰财险 (HUATAI)** - XML API 接口
5. **中华联合 (SINOSIG)** - XML API 接口
6. **史带财险 (STARR)** - JSON API 接口
7. **保代云服 (BDYF)** - 第三方平台自动化
8. **中意雇主 (GROUP_ZHONGYI)** - 雇主责任险 API

### 3.2 数据转换规则

#### 3.2.1 通用业务规则

系统实现了一套通用的业务规则处理机制，详细配置请参考 `config/business_rules_config.json` 文件：

**费率转换规则**:

```
平台费率(万分之几) ÷ 10 = 保险公司费率(千分之几)
```

**时间处理规则**:

- 起运时间如果是当天，延后 1 小时
- 如果当前分钟数超过 50 分钟，再延后 1 小时
- 如果调整后时间超过 24 点，改为次日 00:00

**发票号格式规则**:

- 保呀平台: `invNo + ' / ' + freightNo`
- 其他平台: `orderNo + ' / ' + invNo`

#### 3.2.2 特殊处理逻辑

不同保险公司和账号有特定的处理逻辑：

**太保财险特殊处理**:

- CDYF 账号需要特殊的货物覆盖配置 JSON
- 不同账号有不同的协议号和费率配置

**平安财险特殊处理**:

- 特定账号有固定的投保人信息
- 跨境电商模式有特殊的失败检测时间(2 小时)

**华泰财险特殊处理**:

- 复杂的货物类型映射表
- 运输方式和车型的组合映射

**中华联合特殊处理**:

- 需要对%符号进行 HTML 编码
- 不同产品有不同的系统标识

**史带财险特殊处理**:

- 需要生成 GUID 和 MD5 签名
- 区分企业和个人的证件类型处理

## 4. Go 重构技术方案

### 4.1 微服务架构设计

#### 4.1.1 架构概述

上面的微服务架构图展示了重构后的系统架构。采用分层设计，包括：

- **负载均衡层**: 处理外部请求分发
- **API 网关层**: 统一入口，路由管理和限流控制
- **认证服务**: JWT 认证和权限管理
- **配置服务**: 集中配置管理和热更新
- **核心业务服务**: 保险业务处理、适配器服务、通知服务
- **调度服务**: 定时任务和异常检测
- **数据层**: 数据库服务和消息队列
- **存储层**: MySQL 和 Redis
- **监控层**: Prometheus、Grafana 和 Jaeger

#### 4.1.2 技术栈选择

**Web 框架**: Gin

- 高性能 HTTP 框架
- 中间件支持丰富
- 路由功能强大

**数据库**: GORM + MySQL

- 成熟的 ORM 框架
- 支持数据库迁移
- 连接池管理

**消息队列**: Redis + Asynq

- 基于 Redis 的任务队列
- 支持重试和死信队列
- 监控界面友好

**配置管理**: Viper

- 支持多种配置格式
- 环境变量支持
- 热重载功能

**日志**: Zap

- 高性能结构化日志
- 多种输出格式
- 日志级别控制

**监控**: Prometheus + Grafana

- 指标收集和存储
- 可视化监控面板
- 告警规则配置

**链路追踪**: Jaeger

- 分布式链路追踪
- 性能分析
- 依赖关系图

**容器化**: Docker + Kubernetes

- 容器化部署
- 服务编排
- 自动扩缩容

### 4.2 核心数据结构

#### 4.2.1 保险请求结构

```go
type InsuranceRequest struct {
    PlatformID     int64     `json:"platform_id" validate:"required"`
    ProductID      int64     `json:"product_id" validate:"required"`
    OrderNo        string    `json:"order_no" validate:"required"`
    Mode           string    `json:"mode" validate:"required"`
    HolderName     string    `json:"holder_name" validate:"required"`
    RecognizeeName string    `json:"recognizee_name" validate:"required"`
    GoodsName      string    `json:"goods_name" validate:"required"`
    InsuredAmount  float64   `json:"insured_amount" validate:"required,gt=0"`
    Premium        float64   `json:"premium" validate:"required,gt=0"`
    DepartureDate  int64     `json:"departure_date" validate:"required"`
    FromLoc        string    `json:"from_loc" validate:"required"`
    ToLoc          string    `json:"to_loc" validate:"required"`
    ViaLoc         *string   `json:"via_loc,omitempty"`
    Transport      *string   `json:"transport,omitempty"`
    InvNo          *string   `json:"inv_no,omitempty"`
    Notifies       []Notify  `json:"notifies" validate:"required,dive"`
}

type Notify struct {
    OrderNo string `json:"order_no" validate:"required"`
    URL     string `json:"url" validate:"required,url"`
}
```

#### 4.2.2 适配器接口

```go
type InsuranceAdapter interface {
    Process(ctx context.Context, data InsuranceData) (AdapterResponse, error)
    Validate(data InsuranceData) error
    Transform(data InsuranceData) (map[string]interface{}, error)
    GetMode() string
    GetCompany() string
}

type AdapterResponse struct {
    Success   bool                   `json:"success"`
    Data      map[string]interface{} `json:"data,omitempty"`
    Error     string                 `json:"error,omitempty"`
    ApplyNo   string                 `json:"apply_no,omitempty"`
    PolicyNo  string                 `json:"policy_no,omitempty"`
}
```

#### 4.2.3 配置结构

```go
type InsuranceCompanyConfig struct {
    Name        string            `yaml:"name"`
    Modes       []string          `yaml:"modes"`
    Type        string            `yaml:"type"` // automation, api
    Controller  string            `yaml:"controller"`
    DataFormat  string            `yaml:"data_format,omitempty"` // xml, json
    Features    CompanyFeatures   `yaml:"features"`
    Accounts    map[string]Account `yaml:"accounts,omitempty"`
    Mappings    CompanyMappings   `yaml:"mappings,omitempty"`
}

type CompanyFeatures struct {
    Domestic       bool `yaml:"domestic"`
    International  bool `yaml:"international"`
    AutoEntry      bool `yaml:"auto_entry"`
    APIIntegration bool `yaml:"api_integration"`
}
```

### 4.3 错误处理和重试机制

#### 4.3.1 错误分类

```go
type ErrorType string

const (
    ErrorTypeBusiness ErrorType = "business"
    ErrorTypeSystem   ErrorType = "system"
    ErrorTypeNetwork  ErrorType = "network"
)

type AutoInsError struct {
    Type    ErrorType `json:"type"`
    Code    string    `json:"code"`
    Message string    `json:"message"`
    Details string    `json:"details,omitempty"`
}
```

#### 4.3.2 重试策略

```go
type RetryConfig struct {
    MaxRetries        int           `yaml:"max_retries"`
    InitialDelay      time.Duration `yaml:"initial_delay"`
    BackoffMultiplier float64       `yaml:"backoff_multiplier"`
    MaxDelay          time.Duration `yaml:"max_delay"`
}
```

### 4.4 服务间通信

#### 4.4.1 同步通信

- 使用 HTTP/gRPC 进行服务间同步调用
- 实现熔断器模式防止级联故障
- 超时控制和重试机制

#### 4.4.2 异步通信

- 使用消息队列进行异步处理
- 事件驱动架构
- 最终一致性保证

## 5. 数据字典

### 5.1 数据库表结构

详细的数据库表结构定义请参考 `config/database_schema.json` 文件，包含完整的字段定义、索引和关系说明。

### 5.2 枚举值和状态定义

详细的枚举值和状态定义请参考 `config/business_rules_config.json` 文件，包含：

- **投保方式定义**: 所有支持的保险公司模式
- **保单状态定义**: 状态流转和颜色标识
- **通知状态定义**: 通知推送状态管理
- **平台和产品状态**: 启用/禁用状态管理

### 5.3 业务规则配置

系统的业务规则配置包括：

- **时间处理规则**: 起运时间调整和失败检测超时
- **费率转换规则**: 平台费率到保险公司费率的转换
- **发票号格式规则**: 不同平台的发票号格式化
- **身份验证规则**: 身份证号验证算法

## 6. 固定数据配置

### 6.1 保险公司配置

详细的保险公司配置信息请参考 `config/insurance_companies_config.json` 文件，包含：

- **账户配置**: 各保险公司的账户信息和特殊配置
- **映射表**: 货物类型、运输方式、包装类型等映射关系
- **固定值**: 各保险公司的固定参数配置
- **特殊处理**: 各保险公司的特殊业务逻辑

### 6.2 子系统配置

支持多个子系统的数据同步，包括：

- **优孚系统**: SerialNo 字段，时间戳格式
- **保呀系统**: sys_order_no 字段，日期时间格式
- **保呀新系统**: order_no 字段，提交时间格式

## 7. 实施计划

### 7.1 阶段划分

#### 第一阶段：基础设施搭建 (2 周)

- 搭建 Go 开发环境和 CI/CD 流水线
- 设计微服务架构和服务拆分
- 配置 Docker 容器化环境
- 搭建监控和日志系统

#### 第二阶段：核心服务开发 (4 周)

- 开发 API Gateway 和路由管理
- 开发认证服务和 JWT 管理
- 开发配置服务和热更新机制
- 开发数据库服务和连接池管理

#### 第三阶段：业务服务开发 (6 周)

- 开发保险业务服务和状态管理
- 开发适配器服务和保险公司接入
- 开发通知服务和异步推送
- 开发调度服务和定时任务

#### 第四阶段：数据迁移和测试 (3 周)

- 执行数据库迁移和数据验证
- 进行功能测试和集成测试
- 执行性能测试和压力测试
- 进行安全测试和漏洞扫描

#### 第五阶段：部署和上线 (1 周)

- 生产环境部署和配置
- 灰度发布和流量切换
- 监控告警配置和调优
- 文档完善和用户培训

### 7.2 风险控制

#### 7.2.1 技术风险

- **并行开发**: 新旧系统并行运行，确保业务连续性
- **数据一致性**: 实施双写策略，确保数据同步
- **回滚机制**: 准备快速回滚方案和应急预案
- **性能风险**: 进行充分的性能测试和容量规划

#### 7.2.2 业务风险

- **功能验证**: 逐个保险公司验证功能完整性
- **数据准确性**: 确保数据转换的准确性和完整性
- **用户培训**: 提供充分的用户培训和操作手册
- **业务连续性**: 确保切换过程中业务不中断

### 7.3 成功指标

#### 7.3.1 性能指标

- API 响应时间 < 100ms (P95)
- 系统可用性 > 99.9%
- 错误率 < 0.1%
- 并发处理能力 > 1000 TPS

#### 7.3.2 业务指标

- 保单处理成功率 > 99%
- 自动录单成功率 > 95%
- 通知推送成功率 > 99%
- 数据一致性 > 99.99%

#### 7.3.3 运维指标

- 部署时间 < 10 分钟
- 故障恢复时间 < 5 分钟
- 监控覆盖率 > 95%
- 日志完整性 > 99%

## 8. 总结

### 8.1 重构优势

本重构方案将现有的 PHP/Laravel 单体应用重构为 Go 语言微服务架构，主要优势包括：

1. **性能提升**: Go 语言的高并发特性和更好的资源利用率
2. **可维护性**: 微服务架构便于独立开发、测试和部署
3. **可扩展性**: 服务可以根据负载独立扩缩容
4. **可靠性**: 更好的错误处理和重试机制
5. **监控性**: 完善的监控和链路追踪系统

### 8.2 技术收益

- **开发效率**: 团队可以并行开发不同的微服务
- **部署灵活性**: 可以独立部署和更新各个服务
- **故障隔离**: 单个服务故障不会影响整个系统
- **技术栈多样性**: 不同服务可以选择最适合的技术栈

### 8.3 业务价值

- **响应速度**: 更快的 API 响应时间提升用户体验
- **系统稳定性**: 更高的可用性保证业务连续性
- **扩展能力**: 支持更多保险公司和更大业务量
- **运维效率**: 自动化运维减少人工干预

### 8.4 后续规划

- **持续优化**: 根据监控数据持续优化性能
- **功能扩展**: 支持更多保险公司和业务场景
- **智能化**: 引入 AI/ML 提升自动化程度
- **国际化**: 支持海外保险公司接入

通过详细的分析和设计，确保重构过程平滑进行，业务功能完整迁移，系统性能得到显著提升。配合完善的配置文件和数据字典，为 Go 重构提供了全面的技术指导和实施方案。
