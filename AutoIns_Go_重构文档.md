# AutoIns 自动录单系统 Go 重构文档

## 1. 项目概述

### 1.1 系统简介
AutoIns 是一个多保险公司自动录单集成系统，主要功能是接收投保平台的保单数据，自动转换为各保险公司所需的格式，并完成自动录单。系统支持多种保险公司的接入，包括人保财险、太保财险、平安财险、华泰财险、中华联合、史带财险等。

### 1.2 当前技术栈
- **后端**: PHP 7 + Laravel 5.5
- **前端**: Bootstrap + jQuery
- **数据库**: MySQL
- **队列**: Laravel Queue
- **自动化**: Python 2.7 + Splinter 0.13

### 1.3 重构目标
将现有 PHP/Laravel 系统重构为 Go 语言微服务架构，提升系统性能、可维护性和扩展性。

## 2. 系统架构分析

### 2.1 当前系统架构

#### 2.1.1 核心模块
1. **报文模块**: 保存和处理投保平台投递的保单数据
2. **推送模块**: 保单生效后回调通知投保平台
3. **平台模块**: 管理投保平台，生成接口参数
4. **产品模块**: 管理投保平台对应的产品配置
5. **邮件模块**: 录单失败时发送邮件通知
6. **管理员模块**: 系统用户管理

#### 2.1.2 数据流转
```
投保平台 → API接口 → 数据验证 → 保险公司适配器 → 自动录单 → 结果回调 → 投保平台
```

#### 2.1.3 保单状态管理
- **0**: 未处理 - 报文尚未提交到保险公司
- **1**: 已提交 - 已有提交到保险公司动作，但不知道是否成功
- **2**: 已完成 - 提交到保险公司，并返回了保单号
- **-1**: 已作废 - 作废保单

### 2.2 数据库设计

#### 2.2.1 核心表结构
```sql
-- 平台表
CREATE TABLE platforms (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    app_id VARCHAR(255) UNIQUE NOT NULL,
    secret_key VARCHAR(255) NOT NULL,
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP NULL
);

-- 产品表
CREATE TABLE products (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_code VARCHAR(255) UNIQUE,
    title VARCHAR(255) NOT NULL,
    account VARCHAR(100) NOT NULL,
    mode VARCHAR(20) NOT NULL,
    config TEXT,
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP NULL
);

-- 报文主表
CREATE TABLE messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    platform_id INT NOT NULL,
    product_id INT NOT NULL,
    mode VARCHAR(20) NOT NULL,
    order_no VARCHAR(50) NOT NULL,
    apply_no VARCHAR(50),
    policy_no VARCHAR(50),
    done_at TIMESTAMP,
    is_locked TINYINT DEFAULT 0,
    status TINYINT DEFAULT 1,
    error_num TINYINT DEFAULT 0,
    handle_num TINYINT DEFAULT 0,
    is_download TINYINT DEFAULT 0,
    insure_callback TEXT,
    is_entry TINYINT DEFAULT 0,
    file_path VARCHAR(255),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP NULL
);

-- 报文附表
CREATE TABLE message_attaches (
    id INT PRIMARY KEY AUTO_INCREMENT,
    message_id INT NOT NULL,
    source TEXT,
    content TEXT,
    callback TEXT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- 通知表
CREATE TABLE notifies (
    id INT PRIMARY KEY AUTO_INCREMENT,
    message_id INT NOT NULL,
    content VARCHAR(1024),
    url VARCHAR(255) NOT NULL,
    done_at TIMESTAMP,
    callback VARCHAR(1024),
    status TINYINT DEFAULT 1,
    order_no VARCHAR(50),
    error_num TINYINT DEFAULT 0,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- 平台产品关联表
CREATE TABLE platform_products (
    id INT PRIMARY KEY AUTO_INCREMENT,
    platform_id INT NOT NULL,
    product_id INT NOT NULL,
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

## 3. 保险公司适配器分析

### 3.1 支持的保险公司

#### 3.1.1 人保财险 (PICC)
- **模式**: AUTO_PICC, AUTO_PICC_INTL
- **类型**: 自动化录单
- **特点**: 支持国内和国际业务
- **账号配置**: 多个账号支持不同业务场景

#### 3.1.2 太保财险 (CPIC)
- **模式**: AUTO_CPIC, AUTO_CPIC_INTL
- **类型**: 自动化录单
- **特点**: 复杂的协议配置和费率计算
- **特殊处理**: CDYF账号有特殊的货物覆盖配置

#### 3.1.3 平安财险 (PINGAN)
- **模式**: AUTO_PINGAN, AUTO_PINGAN_INTL, AUTO_PINGAN_CBEC
- **类型**: 自动化录单
- **特点**: 支持跨境电商业务
- **特殊规则**: 不同账号有不同的证件类型处理

#### 3.1.4 华泰财险 (HUATAI)
- **模式**: API_HUATAI
- **类型**: API接口
- **数据格式**: XML
- **特点**: 货物类型和运输方式映射复杂

#### 3.1.5 中华联合 (SINOSIG)
- **模式**: API_SINOSIG, API_SINOSIG_QZ
- **类型**: API接口
- **数据格式**: XML
- **特点**: 需要特殊的字符编码处理

#### 3.1.6 史带财险 (STARR)
- **模式**: API_STARR
- **类型**: API接口
- **数据格式**: JSON
- **特点**: 需要生成GUID和MD5签名

### 3.2 数据转换规则

#### 3.2.1 通用规则
- **费率转换**: 平台费率(万分之几) ÷ 10 = 保险公司费率(千分之几)
- **时间处理**: 起运时间如果是当天，延后1小时
- **发票号格式**: 
  - 保呀平台: `invNo + ' / ' + freightNo`
  - 其他平台: `orderNo + ' / ' + invNo`

#### 3.2.2 特殊处理
- **太保CDYF账号**: 需要特殊的货物覆盖配置JSON
- **平安特定账号**: 固定投保人信息
- **华泰货物类型**: 复杂的货物类型映射表
- **中华联合**: 需要对%符号进行HTML编码

## 4. Go 重构技术方案

### 4.1 微服务架构设计

#### 4.1.1 服务拆分
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Gateway   │    │  Auth Service   │    │  Config Service │
│                 │    │                 │    │                 │
│ - 路由管理      │    │ - JWT认证       │    │ - 配置管理      │
│ - 限流控制      │    │ - 权限验证      │    │ - 热更新        │
│ - 日志记录      │    │ - 用户管理      │    │ - 版本控制      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Insurance Svc   │    │ Notification    │    │ Scheduler Svc   │
│                 │    │ Service         │    │                 │
│ - 保单处理      │    │ - 推送通知      │    │ - 定时任务      │
│ - 数据转换      │    │ - 邮件发送      │    │ - 异常检测      │
│ - 状态管理      │    │ - 回调处理      │    │ - 数据同步      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Adapter Service │    │ Database Svc    │    │ Message Queue   │
│                 │    │                 │    │                 │
│ - 保险公司适配  │    │ - 数据持久化    │    │ - 异步处理      │
│ - 接口调用      │    │ - 事务管理      │    │ - 重试机制      │
│ - 错误处理      │    │ - 连接池        │    │ - 死信队列      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

#### 4.1.2 技术栈选择
- **Web框架**: Gin
- **数据库**: GORM + MySQL
- **消息队列**: Redis + Asynq
- **配置管理**: Viper
- **日志**: Zap
- **监控**: Prometheus + Grafana
- **链路追踪**: Jaeger
- **容器化**: Docker + Kubernetes

### 4.2 核心数据结构

#### 4.2.1 保险请求结构
```go
type InsuranceRequest struct {
    PlatformID     int64     `json:"platform_id" validate:"required"`
    ProductID      int64     `json:"product_id" validate:"required"`
    OrderNo        string    `json:"order_no" validate:"required"`
    Mode           string    `json:"mode" validate:"required"`
    HolderName     string    `json:"holder_name" validate:"required"`
    RecognizeeName string    `json:"recognizee_name" validate:"required"`
    GoodsName      string    `json:"goods_name" validate:"required"`
    InsuredAmount  float64   `json:"insured_amount" validate:"required,gt=0"`
    Premium        float64   `json:"premium" validate:"required,gt=0"`
    DepartureDate  int64     `json:"departure_date" validate:"required"`
    FromLoc        string    `json:"from_loc" validate:"required"`
    ToLoc          string    `json:"to_loc" validate:"required"`
    ViaLoc         *string   `json:"via_loc,omitempty"`
    Transport      *string   `json:"transport,omitempty"`
    InvNo          *string   `json:"inv_no,omitempty"`
    Notifies       []Notify  `json:"notifies" validate:"required,dive"`
}

type Notify struct {
    OrderNo string `json:"order_no" validate:"required"`
    URL     string `json:"url" validate:"required,url"`
}
```

#### 4.2.2 适配器接口
```go
type InsuranceAdapter interface {
    Process(ctx context.Context, data InsuranceData) (AdapterResponse, error)
    Validate(data InsuranceData) error
    Transform(data InsuranceData) (map[string]interface{}, error)
    GetMode() string
    GetCompany() string
}

type AdapterResponse struct {
    Success   bool                   `json:"success"`
    Data      map[string]interface{} `json:"data,omitempty"`
    Error     string                 `json:"error,omitempty"`
    ApplyNo   string                 `json:"apply_no,omitempty"`
    PolicyNo  string                 `json:"policy_no,omitempty"`
}
```

### 4.3 配置管理

#### 4.3.1 保险公司配置
```go
type InsuranceCompanyConfig struct {
    Name        string            `yaml:"name"`
    Modes       []string          `yaml:"modes"`
    Type        string            `yaml:"type"` // automation, api
    Controller  string            `yaml:"controller"`
    DataFormat  string            `yaml:"data_format,omitempty"` // xml, json
    Features    CompanyFeatures   `yaml:"features"`
    Accounts    map[string]Account `yaml:"accounts,omitempty"`
    Mappings    CompanyMappings   `yaml:"mappings,omitempty"`
}

type CompanyFeatures struct {
    Domestic       bool `yaml:"domestic"`
    International  bool `yaml:"international"`
    AutoEntry      bool `yaml:"auto_entry"`
    APIIntegration bool `yaml:"api_integration"`
}

type Account struct {
    Description string                 `yaml:"description"`
    Config      map[string]interface{} `yaml:"config"`
}
```

#### 4.3.2 业务规则配置
```go
type BusinessRules struct {
    TimeProcessing    TimeProcessingRules    `yaml:"time_processing"`
    RateConversion    RateConversionRules    `yaml:"rate_conversion"`
    InvoiceFormat     InvoiceFormatRules     `yaml:"invoice_format"`
    AmountFormatting  AmountFormattingRules  `yaml:"amount_formatting"`
}

type TimeProcessingRules struct {
    DepartureTimeAdjustment DepartureTimeAdjustment `yaml:"departure_time_adjustment"`
    FailureDetectionTimeout FailureDetectionTimeout `yaml:"failure_detection_timeout"`
}
```

### 4.4 错误处理和重试机制

#### 4.4.1 错误分类
```go
type ErrorType string

const (
    ErrorTypeBusiness ErrorType = "business"
    ErrorTypeSystem   ErrorType = "system"
    ErrorTypeNetwork  ErrorType = "network"
)

type AutoInsError struct {
    Type    ErrorType `json:"type"`
    Code    string    `json:"code"`
    Message string    `json:"message"`
    Details string    `json:"details,omitempty"`
}
```

#### 4.4.2 重试策略
```go
type RetryConfig struct {
    MaxRetries      int           `yaml:"max_retries"`
    InitialDelay    time.Duration `yaml:"initial_delay"`
    BackoffMultiplier float64     `yaml:"backoff_multiplier"`
    MaxDelay        time.Duration `yaml:"max_delay"`
}
```

## 5. 数据字典

### 5.1 核心实体

#### 5.1.1 Platform (平台)
| 字段名 | 类型 | 长度 | 必填 | 说明 |
|--------|------|------|------|------|
| id | INT | - | 是 | 主键ID |
| title | VARCHAR | 255 | 是 | 平台名称 |
| app_id | VARCHAR | 255 | 是 | 应用ID，唯一标识 |
| secret_key | VARCHAR | 255 | 是 | 密钥 |
| status | TINYINT | - | 是 | 状态：1-禁用，2-启用 |

#### 5.1.2 Product (产品)
| 字段名 | 类型 | 长度 | 必填 | 说明 |
|--------|------|------|------|------|
| id | INT | - | 是 | 主键ID |
| product_code | VARCHAR | 255 | 否 | 产品代码，唯一 |
| title | VARCHAR | 255 | 是 | 产品名称 |
| account | VARCHAR | 100 | 是 | 账户标识 |
| mode | VARCHAR | 20 | 是 | 投保方式 |
| config | TEXT | - | 否 | 产品配置JSON |
| status | TINYINT | - | 是 | 状态：1-禁用，2-启用 |

#### 5.1.3 Message (报文)
| 字段名 | 类型 | 长度 | 必填 | 说明 |
|--------|------|------|------|------|
| id | INT | - | 是 | 主键ID |
| platform_id | INT | - | 是 | 平台ID |
| product_id | INT | - | 是 | 产品ID |
| mode | VARCHAR | 20 | 是 | 投保方式 |
| order_no | VARCHAR | 50 | 是 | 订单号 |
| apply_no | VARCHAR | 50 | 否 | 投保单号 |
| policy_no | VARCHAR | 50 | 否 | 保单号 |
| status | TINYINT | - | 是 | 状态：0-未处理，1-已提交，2-已完成，-1-已作废 |
| error_num | TINYINT | - | 是 | 错误次数 |
| handle_num | TINYINT | - | 是 | 处理次数 |
| is_entry | TINYINT | - | 是 | 是否录入：0-否，1-是 |

### 5.2 枚举值定义

#### 5.2.1 投保方式 (Mode)
```json
{
  "AUTO_PICC": "人保自动录单",
  "AUTO_PICC_INTL": "人保国际自动录单",
  "AUTO_CPIC": "太保自动录单",
  "AUTO_CPIC_INTL": "太保国际自动录单",
  "AUTO_PINGAN": "平安自动录单",
  "AUTO_PINGAN_INTL": "平安国际自动录单",
  "AUTO_PINGAN_CBEC": "平安跨境电商",
  "API_HUATAI": "华泰API接口",
  "API_SINOSIG": "中华联合API接口",
  "API_SINOSIG_QZ": "中华联合钦州API接口",
  "API_STARR": "史带API接口",
  "AUTO_BDYF": "保代云服自动录单",
  "API_GROUP_ZY": "中意雇主API接口"
}
```

#### 5.2.2 保单状态 (Status)
```json
{
  "0": {
    "name": "未处理",
    "description": "报文已创建，等待处理",
    "color": "gray"
  },
  "1": {
    "name": "已提交",
    "description": "已提交到保险公司，等待结果",
    "color": "blue"
  },
  "2": {
    "name": "已完成",
    "description": "已获取保单号，处理完成",
    "color": "green"
  },
  "-1": {
    "name": "已作废",
    "description": "处理失败，已作废",
    "color": "red"
  }
}
```

#### 5.2.3 通知状态 (Notify Status)
```json
{
  "0": {
    "name": "未处理",
    "description": "通知未处理"
  },
  "1": {
    "name": "待发送",
    "description": "等待发送通知"
  },
  "2": {
    "name": "已发送",
    "description": "通知已发送"
  },
  "3": {
    "name": "发送失败",
    "description": "通知发送失败"
  },
  "4": {
    "name": "中意雇主待发送",
    "description": "中意雇主特殊状态"
  }
}
```

## 6. 固定数据配置

### 6.1 太保财险配置
```json
{
  "accounts": {
    "SHTB": {
      "protocol_no": "A|3010200|C20180144-19001P000431|CAGHYX190032",
      "unit_code": "3010200",
      "franchise": "本保单其他承保条件同协议。",
      "rate_info": ["0.5", "3000000", "50"]
    },
    "YRWL": {
      "protocol_no": "A|3010100|C20190295|0",
      "unit_code": "3010100",
      "franchise": "本保单其他承保条件同协议; 每次事故绝对免赔额为人民币1000或损失金额的10%，两者取高;",
      "rate_info": ["0.15", "500000", "3"]
    },
    "CDYF": {
      "protocol_no": "C|3010100|CSHHHYX2024P000473|0|CSHHHYX2024Q000452",
      "unit_code": "3010100",
      "holder_name": "成都优孚世纪信息技术有限公司",
      "special_processing": true
    }
  },
  "pack_type_mapping": {
    "裸装": "05",
    "散装": "04",
    "纸箱": "01",
    "木箱": "01",
    "捆包": "08",
    "袋装": "02",
    "篓装": "01",
    "托盘": "03",
    "桶装": "06",
    "罐装": "07"
  },
  "transport_type_mapping": {
    "3": "1",
    "4": "5",
    "5": "4",
    "6": "3"
  }
}
```

### 6.2 华泰财险配置
```json
{
  "goods_type_mapping": {
    "纺织原料及纺织制品": ["SX001411", "SX00140065"],
    "机器设备及其零件、附件": ["SX001416", "SX00140087"],
    "食品": ["SX001404", "SX00140019"],
    "化学工业及其相关工业产品": ["SX001406", "SX00140040"],
    "塑料及其制品;橡胶及其制品": ["SX001407", "SX00140041"],
    "玻璃及玻璃制品": ["SX001413", "SX00140072"],
    "新车": ["SX001417", "SX00140089"],
    "二手车": ["SX001417", "SX00140089"]
  },
  "transport_type_mapping": {
    "3": {
      "厢式货车": ["SX001501", "01"],
      "非厢式货车": ["SX001501", "05"]
    },
    "4": {
      "厢式货车": ["SX001503", "01"],
      "非厢式货车": ["SX001503", "02"]
    },
    "5": {
      "厢式货车": ["SX001502", "01"],
      "非厢式货车": ["SX001502", "02"]
    },
    "6": {
      "厢式货车": ["SX001505", "01"],
      "非厢式货车": ["SX001505", "02"]
    }
  },
  "clause_mapping": {
    "JBX": ["SX300211", "基本险"],
    "ZHX": ["SX300212", "综合险"]
  },
  "fixed_values": {
    "survey_address_id": "501422495713",
    "survey_address": "17/F,Block B,Center Plaza 161 Linhexi Av.,Tianhe District, Guangzhou TEL:4006095509 FAX: 020-87567201",
    "from_country": "HTC01",
    "to_country": "HTC01",
    "id_type": "99"
  }
}
```

### 6.3 中华联合配置
```json
{
  "product_settings": {
    "2020022194297": {
      "name": "保呀产品",
      "sys_flag": "BAOYA",
      "com_code": "07710200",
      "protocol_no": "10771YAB02023000006",
      "operate_code": "BAOYA",
      "underwrite_status": "00"
    },
    "default": {
      "name": "阿拉丁产品",
      "sys_flag": "aladdin",
      "com_code": "07514300",
      "protocol_no": "10771YAB02020000035",
      "operate_code": "aladdin",
      "underwrite_status": "01"
    }
  },
  "packer_code_mapping": {
    "裸装": ["024", "标准包装"],
    "散装": ["024", "标准包装"],
    "纸箱": ["002", "纸箱"],
    "木箱": ["001", "木箱"],
    "袋装": ["023", "袋子"],
    "托盘": ["020", "托盘"],
    "桶装": ["019", "桶"]
  }
}
```

### 6.4 史带财险配置
```json
{
  "fixed_config": {
    "partner_code": "YOUFU",
    "partner_key": "km2a0f13c9dkb8",
    "product_code": 60021,
    "operation_type": "006",
    "pay_treatment_method": "2",
    "duration_type": "M",
    "duration": 1,
    "cargo_transport_way": "3",
    "email": "<EMAIL>"
  },
  "identity_processing": {
    "company_part_type": "QY",
    "company_card_type": "104",
    "individual_part_type": "GR",
    "individual_card_type": "1"
  }
}
```

## 7. 实施计划

### 7.1 阶段划分

#### 第一阶段：基础设施搭建 (2周)
- 搭建Go开发环境
- 设计微服务架构
- 搭建CI/CD流水线
- 配置监控和日志系统

#### 第二阶段：核心服务开发 (4周)
- 开发API Gateway
- 开发认证服务
- 开发配置服务
- 开发数据库服务

#### 第三阶段：业务服务开发 (6周)
- 开发保险业务服务
- 开发适配器服务
- 开发通知服务
- 开发调度服务

#### 第四阶段：数据迁移和测试 (3周)
- 数据库迁移
- 功能测试
- 性能测试
- 集成测试

#### 第五阶段：部署和上线 (1周)
- 生产环境部署
- 灰度发布
- 监控告警配置
- 文档完善

### 7.2 风险控制

#### 7.2.1 技术风险
- **并行开发**: 新旧系统并行运行，确保业务连续性
- **数据一致性**: 实施双写策略，确保数据同步
- **回滚机制**: 准备快速回滚方案

#### 7.2.2 业务风险
- **功能验证**: 逐个保险公司验证功能完整性
- **性能测试**: 确保新系统性能不低于旧系统
- **用户培训**: 提供充分的用户培训和文档

### 7.3 成功指标

#### 7.3.1 性能指标
- API响应时间 < 100ms
- 系统可用性 > 99.9%
- 错误率 < 0.1%

#### 7.3.2 业务指标
- 保单处理成功率 > 99%
- 自动录单成功率 > 95%
- 通知推送成功率 > 99%

## 8. 总结

本重构方案将现有的PHP/Laravel单体应用重构为Go语言微服务架构，主要优势包括：

1. **性能提升**: Go语言的高并发特性和更好的资源利用率
2. **可维护性**: 微服务架构便于独立开发、测试和部署
3. **可扩展性**: 服务可以根据负载独立扩缩容
4. **可靠性**: 更好的错误处理和重试机制
5. **监控性**: 完善的监控和链路追踪系统

通过详细的分析和设计，确保重构过程平滑进行，业务功能完整迁移，系统性能得到显著提升。
