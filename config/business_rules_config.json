{"business_rules": {"time_processing": {"departure_time_adjustment": {"same_day_offset_hours": 1, "minute_threshold": 50, "additional_offset_when_over_threshold_hours": 1, "next_day_reset_time": "00:00", "description": "起运时间调整规则：如果起运时间是当天，延后1小时；如果当前分钟数超过50分钟，再延后1小时；如果调整后时间超过24点，改为次日00:00"}, "failure_detection_timeout": {"pingan_cbec_hours": 2, "other_modes_minutes": 10, "description": "失败检测时间规则：平安跨境电商2小时后算失败，其他模式10分钟后算失败"}}, "rate_conversion": {"formula": "platform_rate(万分之几) / 10 = insurance_rate(千分之几)", "description": "平台费率单位是万分之几，保险公司费率单位是千分之几", "example": {"platform_rate": 15, "insurance_rate": 1.5, "calculation": "15 / 10 = 1.5"}}, "invoice_number_format": {"platform_5_baoya": {"format": "invNo + ' / ' + freightNo", "description": "保呀平台使用发票号+运单号格式"}, "other_platforms": {"format": "orderNo + ' / ' + invNo", "description": "其他平台使用订单号+发票号格式"}}, "amount_formatting": {"precision": 2, "function": "sprintf('%.2f', floatval(amount))", "description": "金额格式化保留2位小数"}, "identity_validation": {"id_card_18_digit": {"pattern": "^\\d{17}[\\dXx]$", "validation": "checksum_algorithm", "description": "18位身份证号验证"}, "id_card_15_digit": {"pattern": "^\\d{15}$", "description": "15位身份证号验证"}}}, "status_definitions": {"message_status": {"0": {"name": "未处理", "description": "报文已创建，等待处理", "color": "gray", "next_states": [1, -1]}, "1": {"name": "已提交", "description": "已提交到保险公司，等待结果", "color": "blue", "next_states": [2, -1]}, "2": {"name": "已完成", "description": "已获取保单号，处理完成", "color": "green", "next_states": []}, "-1": {"name": "已作废", "description": "处理失败，已作废", "color": "red", "next_states": []}}, "notify_status": {"0": {"name": "未处理", "description": "通知未处理", "color": "gray", "next_states": [1]}, "1": {"name": "待发送", "description": "等待发送通知", "color": "yellow", "next_states": [2, 3]}, "2": {"name": "已发送", "description": "通知已发送", "color": "green", "next_states": []}, "3": {"name": "发送失败", "description": "通知发送失败", "color": "red", "next_states": [1]}, "4": {"name": "中意雇主待发送", "description": "中意雇主特殊状态", "color": "orange", "next_states": [2, 3]}}, "platform_status": {"1": {"name": "禁用", "description": "平台已禁用", "color": "red"}, "2": {"name": "启用", "description": "平台已启用", "color": "green"}}, "product_status": {"1": {"name": "禁用", "description": "产品已禁用", "color": "red"}, "2": {"name": "启用", "description": "产品已启用", "color": "green"}}}, "mode_definitions": {"AUTO_PICC": {"name": "人保自动录单", "company": "PICC", "type": "automation", "description": "人保财险国内自动录单"}, "AUTO_PICC_INTL": {"name": "人保国际自动录单", "company": "PICC", "type": "automation", "description": "人保财险国际自动录单"}, "AUTO_CPIC": {"name": "太保自动录单", "company": "CPIC", "type": "automation", "description": "太保财险国内自动录单"}, "AUTO_CPIC_INTL": {"name": "太保国际自动录单", "company": "CPIC", "type": "automation", "description": "太保财险国际自动录单"}, "AUTO_PINGAN": {"name": "平安自动录单", "company": "PINGAN", "type": "automation", "description": "平安财险国内自动录单"}, "AUTO_PINGAN_INTL": {"name": "平安国际自动录单", "company": "PINGAN", "type": "automation", "description": "平安财险国际自动录单"}, "AUTO_PINGAN_CBEC": {"name": "平安跨境电商", "company": "PINGAN", "type": "automation", "description": "平安财险跨境电商自动录单"}, "API_HUATAI": {"name": "华泰API接口", "company": "HUATAI", "type": "api", "data_format": "xml", "description": "华泰财险API接口录单"}, "API_SINOSIG": {"name": "中华联合API接口", "company": "SINOSIG", "type": "api", "data_format": "xml", "description": "中华联合API接口录单"}, "API_SINOSIG_QZ": {"name": "中华联合钦州API接口", "company": "SINOSIG", "type": "api", "data_format": "xml", "description": "中华联合钦州API接口录单"}, "API_STARR": {"name": "史带API接口", "company": "STARR", "type": "api", "data_format": "json", "description": "史带财险API接口录单"}, "AUTO_BDYF": {"name": "保代云服自动录单", "company": "BDYF", "type": "automation", "description": "保代云服第三方平台自动录单"}, "API_GROUP_ZY": {"name": "中意雇主API接口", "company": "GROUP_ZHONGYI", "type": "api", "data_format": "json", "description": "中意雇主责任险API接口录单"}}, "subsystem_config": {"DataYoufu": {"order_field": "SerialNo", "time_field": "DateTime", "is_timestamp": true, "platform_id": "INS1569686500", "email": "<EMAIL>", "description": "优孚子系统配置"}, "DataBaoya": {"order_field": "sys_order_no", "time_field": "created_at", "is_timestamp": false, "platform_id": "INS1569723235", "email": "<EMAIL>", "description": "保呀子系统配置"}, "DataBaoyaNew": {"order_field": "order_no", "time_field": "submitted_at", "is_timestamp": false, "platform_id": "INS1635757943", "email": "<EMAIL>", "description": "保呀新系统配置"}}, "email_config": {"failure_notification_emails": ["<EMAIL>", "er<PERSON><PERSON>@foxmail.com"], "system_emails": {"baodehao": "<EMAIL>", "baoya": "<EMAIL>"}}}