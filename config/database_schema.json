{"database_schema": {"tables": {"platforms": {"description": "投保平台管理表", "fields": {"id": {"type": "INT", "primary_key": true, "auto_increment": true, "description": "主键ID"}, "title": {"type": "VARCHAR(255)", "nullable": false, "description": "平台名称"}, "app_id": {"type": "VARCHAR(255)", "nullable": false, "unique": true, "description": "应用ID，唯一标识"}, "secret_key": {"type": "VARCHAR(255)", "nullable": false, "description": "密钥，用于JWT认证"}, "status": {"type": "TINYINT", "default": 1, "description": "状态：1-禁用，2-启用"}, "created_at": {"type": "TIMESTAMP", "description": "创建时间"}, "updated_at": {"type": "TIMESTAMP", "description": "更新时间"}, "deleted_at": {"type": "TIMESTAMP", "nullable": true, "description": "软删除时间"}}, "indexes": [{"name": "idx_app_id", "fields": ["app_id"], "unique": true}, {"name": "idx_status", "fields": ["status"]}]}, "products": {"description": "保险产品配置表", "fields": {"id": {"type": "INT", "primary_key": true, "auto_increment": true, "description": "主键ID"}, "product_code": {"type": "VARCHAR(255)", "nullable": true, "unique": true, "description": "产品代码，系统生成的唯一标识"}, "title": {"type": "VARCHAR(255)", "nullable": false, "description": "产品名称"}, "account": {"type": "VARCHAR(100)", "nullable": false, "description": "账户标识，对应保险公司账户"}, "mode": {"type": "VARCHAR(20)", "nullable": false, "description": "投保方式，如AUTO_CPIC、API_HUATAI等"}, "config": {"type": "TEXT", "nullable": true, "description": "产品配置JSON，存储特殊配置信息"}, "status": {"type": "TINYINT", "default": 1, "description": "状态：1-禁用，2-启用"}, "created_at": {"type": "TIMESTAMP", "description": "创建时间"}, "updated_at": {"type": "TIMESTAMP", "description": "更新时间"}, "deleted_at": {"type": "TIMESTAMP", "nullable": true, "description": "软删除时间"}}, "indexes": [{"name": "idx_product_code", "fields": ["product_code"], "unique": true}, {"name": "idx_account_mode", "fields": ["account", "mode"], "unique": true}, {"name": "idx_status", "fields": ["status"]}]}, "messages": {"description": "保单报文主表", "fields": {"id": {"type": "INT", "primary_key": true, "auto_increment": true, "description": "主键ID"}, "platform_id": {"type": "INT", "nullable": false, "description": "平台ID，关联platforms表"}, "product_id": {"type": "INT", "nullable": false, "description": "产品ID，关联products表"}, "mode": {"type": "VARCHAR(20)", "nullable": false, "description": "投保方式"}, "order_no": {"type": "VARCHAR(50)", "nullable": false, "description": "订单号，平台提供的唯一标识"}, "apply_no": {"type": "VARCHAR(50)", "nullable": true, "description": "投保单号，保险公司返回"}, "policy_no": {"type": "VARCHAR(50)", "nullable": true, "description": "保单号，保险公司返回"}, "done_at": {"type": "TIMESTAMP", "nullable": true, "description": "完成时间"}, "is_locked": {"type": "TINYINT", "default": 0, "description": "是否锁定：0-否，1-是"}, "status": {"type": "TINYINT", "default": 1, "description": "状态：0-未处理，1-已提交，2-已完成，-1-已作废"}, "error_num": {"type": "TINYINT", "default": 0, "description": "错误次数"}, "handle_num": {"type": "TINYINT", "default": 0, "description": "处理次数"}, "is_download": {"type": "TINYINT", "default": 0, "description": "是否下载：0-否，1-是"}, "insure_callback": {"type": "TEXT", "nullable": true, "description": "保险公司回调数据"}, "is_entry": {"type": "TINYINT", "default": 0, "description": "是否录入：0-否，1-是"}, "file_path": {"type": "VARCHAR(255)", "nullable": true, "description": "文件路径"}, "created_at": {"type": "TIMESTAMP", "description": "创建时间"}, "updated_at": {"type": "TIMESTAMP", "description": "更新时间"}, "deleted_at": {"type": "TIMESTAMP", "nullable": true, "description": "软删除时间"}}, "indexes": [{"name": "idx_platform_product", "fields": ["platform_id", "product_id"]}, {"name": "idx_order_no", "fields": ["order_no"]}, {"name": "idx_status", "fields": ["status"]}, {"name": "idx_mode", "fields": ["mode"]}, {"name": "idx_created_at", "fields": ["created_at"]}]}, "message_attaches": {"description": "保单报文附表，存储详细数据", "fields": {"id": {"type": "INT", "primary_key": true, "auto_increment": true, "description": "主键ID"}, "message_id": {"type": "INT", "nullable": false, "description": "报文ID，关联messages表"}, "source": {"type": "TEXT", "nullable": true, "description": "原始数据，平台提交的JSON数据"}, "content": {"type": "TEXT", "nullable": true, "description": "处理后的数据，转换为保险公司格式"}, "callback": {"type": "TEXT", "nullable": true, "description": "回调数据，保险公司返回的结果"}, "created_at": {"type": "TIMESTAMP", "description": "创建时间"}, "updated_at": {"type": "TIMESTAMP", "description": "更新时间"}}, "indexes": [{"name": "idx_message_id", "fields": ["message_id"]}]}, "notifies": {"description": "通知推送表", "fields": {"id": {"type": "INT", "primary_key": true, "auto_increment": true, "description": "主键ID"}, "message_id": {"type": "INT", "nullable": false, "description": "报文ID，关联messages表"}, "content": {"type": "VARCHAR(1024)", "nullable": true, "description": "通知内容"}, "url": {"type": "VARCHAR(255)", "nullable": false, "description": "回调URL"}, "done_at": {"type": "TIMESTAMP", "nullable": true, "description": "完成时间"}, "callback": {"type": "VARCHAR(1024)", "nullable": true, "description": "回调结果"}, "status": {"type": "TINYINT", "default": 1, "description": "状态：0-未处理，1-待发送，2-已发送，3-发送失败，4-中意雇主待发送"}, "order_no": {"type": "VARCHAR(50)", "nullable": true, "description": "订单号"}, "error_num": {"type": "TINYINT", "default": 0, "description": "错误次数"}, "created_at": {"type": "TIMESTAMP", "description": "创建时间"}, "updated_at": {"type": "TIMESTAMP", "description": "更新时间"}}, "indexes": [{"name": "idx_message_id", "fields": ["message_id"]}, {"name": "idx_status", "fields": ["status"]}, {"name": "idx_order_no", "fields": ["order_no"]}]}, "platform_products": {"description": "平台产品关联表", "fields": {"id": {"type": "INT", "primary_key": true, "auto_increment": true, "description": "主键ID"}, "platform_id": {"type": "INT", "nullable": false, "description": "平台ID"}, "product_id": {"type": "INT", "nullable": false, "description": "产品ID"}, "status": {"type": "TINYINT", "default": 1, "description": "状态：1-禁用，2-启用"}, "created_at": {"type": "TIMESTAMP", "description": "创建时间"}, "updated_at": {"type": "TIMESTAMP", "description": "更新时间"}}, "indexes": [{"name": "idx_platform_product", "fields": ["platform_id", "product_id"], "unique": true}]}, "users": {"description": "系统用户表", "fields": {"id": {"type": "INT", "primary_key": true, "auto_increment": true, "description": "主键ID"}, "name": {"type": "VARCHAR(255)", "nullable": false, "description": "用户名"}, "email": {"type": "VARCHAR(255)", "nullable": false, "unique": true, "description": "邮箱"}, "password": {"type": "VARCHAR(255)", "nullable": false, "description": "密码哈希"}, "remember_token": {"type": "VARCHAR(100)", "nullable": true, "description": "记住登录令牌"}, "created_at": {"type": "TIMESTAMP", "description": "创建时间"}, "updated_at": {"type": "TIMESTAMP", "description": "更新时间"}}, "indexes": [{"name": "idx_email", "fields": ["email"], "unique": true}]}}, "relationships": {"messages_platform": {"type": "belongs_to", "from_table": "messages", "from_field": "platform_id", "to_table": "platforms", "to_field": "id", "description": "报文属于某个平台"}, "messages_product": {"type": "belongs_to", "from_table": "messages", "from_field": "product_id", "to_table": "products", "to_field": "id", "description": "报文属于某个产品"}, "message_attaches_message": {"type": "belongs_to", "from_table": "message_attaches", "from_field": "message_id", "to_table": "messages", "to_field": "id", "description": "报文附表属于某个报文"}, "notifies_message": {"type": "belongs_to", "from_table": "notifies", "from_field": "message_id", "to_table": "messages", "to_field": "id", "description": "通知属于某个报文"}, "platform_products_platform": {"type": "belongs_to", "from_table": "platform_products", "from_field": "platform_id", "to_table": "platforms", "to_field": "id", "description": "平台产品关联属于某个平台"}, "platform_products_product": {"type": "belongs_to", "from_table": "platform_products", "from_field": "product_id", "to_table": "products", "to_field": "id", "description": "平台产品关联属于某个产品"}}}}