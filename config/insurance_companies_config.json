{"insurance_companies": {"CPIC": {"name": "太保财险", "modes": ["AUTO_CPIC", "AUTO_CPIC_INTL"], "type": "automation", "controller": "CPICController", "features": {"domestic": true, "international": true, "auto_entry": true, "api_integration": false}, "accounts": {"SHTB": {"description": "上海太保账户", "protocol_no": "A|3010200|C20180144-19001P000431|CAGHYX190032", "unit_code": "3010200", "franchise": "本保单其他承保条件同协议。", "rate_info": ["0.5", "3000000", "50"]}, "YRWL": {"description": "优然物流账户", "protocol_no": "A|3010100|C20190295|0", "unit_code": "3010100", "franchise": "本保单其他承保条件同协议; 每次事故绝对免赔额为人民币1000或损失金额的10%，两者取高;", "rate_info": ["0.15", "500000", "3"]}, "CDYF": {"description": "成都优孚账户", "protocol_no": "C|3010100|CSHHHYX2024P000473|0|CSHHHYX2024Q000452", "unit_code": "3010100", "holder_name": "成都优孚世纪信息技术有限公司", "special_processing": true, "goods_coverage_config": {"coverage_type": "special", "coverage_details": "特殊货物覆盖配置"}}}, "mappings": {"pack_type": {"裸装": "05", "散装": "04", "纸箱": "01", "木箱": "01", "捆包": "08", "袋装": "02", "篓装": "01", "托盘": "03", "桶装": "06", "罐装": "07"}, "transport_type": {"3": "1", "4": "5", "5": "4", "6": "3"}}}, "PICC": {"name": "人保财险", "modes": ["AUTO_PICC", "AUTO_PICC_INTL"], "type": "automation", "controller": "PICCController", "features": {"domestic": true, "international": true, "auto_entry": true, "api_integration": false}, "accounts": {"SHTB": {"description": "上海人保账户", "config": {"default_settings": true}}}}, "PINGAN": {"name": "平安财险", "modes": ["AUTO_PINGAN", "AUTO_PINGAN_INTL", "AUTO_PINGAN_CBEC"], "type": "automation", "controller": "PINGANController", "features": {"domestic": true, "international": true, "cbec": true, "auto_entry": true, "api_integration": false}, "accounts": {"SHTB": {"description": "上海平安账户", "holder_name": "上海太保货运代理有限公司", "holder_id": "91310115MA1FL6LQ5X", "id_type": "business_license"}, "YRWL": {"description": "优然物流账户", "holder_name": "上海优然物流有限公司", "holder_id": "91310115MA1FL6LQ5X", "id_type": "business_license"}, "CDYF": {"description": "成都优孚账户", "holder_name": "成都优孚世纪信息技术有限公司", "holder_id": "91510100MA61R8LN8H", "id_type": "business_license"}}, "special_rules": {"failure_detection_timeout": "2小时", "cbec_mode_timeout": "2小时", "other_modes_timeout": "10分钟"}}, "HUATAI": {"name": "华泰财险", "modes": ["API_HUATAI"], "type": "api", "controller": "HUATAIController", "data_format": "xml", "features": {"domestic": true, "international": false, "auto_entry": false, "api_integration": true}, "fixed_values": {"survey_address_id": "************", "survey_address": "17/F,Block B,Center Plaza 161 Linhexi Av.,Tianhe District, Guangzhou TEL:********** FAX: 020-87567201", "from_country": "HTC01", "to_country": "HTC01", "id_type": "99"}, "mappings": {"goods_type": {"纺织原料及纺织制品": ["SX001411", "**********"], "机器设备及其零件、附件": ["SX001416", "**********"], "食品": ["SX001404", "**********"], "化学工业及其相关工业产品": ["SX001406", "**********"], "塑料及其制品;橡胶及其制品": ["SX001407", "**********"], "玻璃及玻璃制品": ["SX001413", "**********"], "新车": ["SX001417", "SX00140089"], "二手车": ["SX001417", "SX00140089"]}, "transport_type": {"3": {"厢式货车": ["SX001501", "01"], "非厢式货车": ["SX001501", "05"]}, "4": {"厢式货车": ["SX001503", "01"], "非厢式货车": ["SX001503", "02"]}, "5": {"厢式货车": ["SX001502", "01"], "非厢式货车": ["SX001502", "02"]}, "6": {"厢式货车": ["SX001505", "01"], "非厢式货车": ["SX001505", "02"]}}, "clause_mapping": {"JBX": ["SX300211", "基本险"], "ZHX": ["SX300212", "综合险"]}}}, "SINOSIG": {"name": "中华联合", "modes": ["API_SINOSIG", "API_SINOSIG_QZ"], "type": "api", "controller": "SinosigController", "data_format": "xml", "features": {"domestic": true, "international": false, "auto_entry": false, "api_integration": true}, "product_settings": {"2020022194297": {"name": "保呀产品", "sys_flag": "BAOYA", "com_code": "07710200", "protocol_no": "10771YAB02023000006", "operate_code": "BAOYA", "underwrite_status": "00"}, "default": {"name": "阿拉丁产品", "sys_flag": "<PERSON><PERSON><PERSON>", "com_code": "07514300", "protocol_no": "10771YAB02020000035", "operate_code": "<PERSON><PERSON><PERSON>", "underwrite_status": "01"}}, "mappings": {"packer_code": {"裸装": ["024", "标准包装"], "散装": ["024", "标准包装"], "纸箱": ["002", "纸箱"], "木箱": ["001", "木箱"], "袋装": ["023", "袋子"], "托盘": ["020", "托盘"], "桶装": ["019", "桶"]}}, "special_processing": {"html_encode_percent": true, "description": "需要对%符号进行HTML编码"}}, "STARR": {"name": "史带财险", "modes": ["API_STARR"], "type": "api", "controller": "STARRController", "data_format": "json", "features": {"domestic": true, "international": false, "auto_entry": false, "api_integration": true}, "fixed_config": {"partner_code": "YOUFU", "partner_key": "km2a0f13c9dkb8", "product_code": 60021, "operation_type": "006", "pay_treatment_method": "2", "duration_type": "M", "duration": 1, "cargo_transport_way": "3", "email": "<EMAIL>"}, "identity_processing": {"company_part_type": "QY", "company_card_type": "104", "individual_part_type": "GR", "individual_card_type": "1"}, "special_processing": {"generate_guid": true, "md5_signature": true, "description": "需要生成GUID和MD5签名"}}, "BDYF": {"name": "保代云服", "modes": ["AUTO_BDYF"], "type": "automation", "controller": "BDYFController", "features": {"domestic": true, "international": false, "auto_entry": true, "api_integration": false}}, "GROUP_ZHONGYI": {"name": "中意雇主", "modes": ["API_GROUP_ZY"], "type": "api", "controller": "GROUP_ZHONGYIController", "data_format": "json", "features": {"domestic": true, "international": false, "auto_entry": false, "api_integration": true, "group_insurance": true}, "special_processing": {"notify_status_4": true, "description": "使用特殊的通知状态4"}}}}