# 保代云服 (BDYF) 适配器详细说明

## 1. 基本信息

- **保险公司**: 保代云服平台
- **适配器类型**: 自动化录单 (AUTO)
- **支持模式**: AUTO_BDYF
- **控制器**: BDYFController.php
- **数据格式**: 网页表单提交
- **特殊性质**: 保险代理平台，非直接保险公司

## 2. 业务流程

### 2.1 录单流程图

```mermaid
graph TD
    A[接收保单数据] --> B[平台账户验证]
    B --> C[产品配置加载]
    C --> D[数据标准化处理]
    D --> E[保代云服格式转换]
    E --> F[自动化录单执行]
    F --> G{录单结果}
    G -->|成功| H[获取保单信息]
    G -->|失败| I[错误分析]
    H --> J[返回保单号]
    I --> K{是否可重试}
    K -->|是| F
    K -->|否| L[返回失败]
```

### 2.2 平台特点

1. **代理平台**: 保代云服是保险代理平台，连接多家保险公司
2. **统一接口**: 提供统一的录单接口，后端对接不同保险公司
3. **产品丰富**: 支持多种保险产品和条款
4. **实时处理**: 支持实时录单和即时出单

## 3. 平台配置

### 3.1 基础配置

```json
{
  "platform_name": "保代云服",
  "platform_code": "BDYF",
  "api_version": "v2.0",
  "supported_products": [
    "cargo_insurance",
    "liability_insurance",
    "comprehensive_insurance"
  ],
  "default_settings": {
    "currency": "CNY",
    "language": "zh-CN",
    "timezone": "Asia/Shanghai"
  }
}
```

**配置说明**:
- `platform_code`: 平台代码，用于系统识别
- `api_version`: API版本，确保兼容性
- `supported_products`: 支持的保险产品类型
- `default_settings`: 默认设置，包括币种、语言、时区

### 3.2 账户配置

```json
{
  "accounts": {
    "SHTB": {
      "description": "上海太保账户",
      "account_id": "SHTB_BDYF_001",
      "access_level": "standard",
      "product_permissions": ["cargo_insurance"],
      "rate_discount": 0.95
    },
    "YRWL": {
      "description": "优然物流账户", 
      "account_id": "YRWL_BDYF_002",
      "access_level": "premium",
      "product_permissions": ["cargo_insurance", "liability_insurance"],
      "rate_discount": 0.90
    }
  }
}
```

**配置说明**:
- `account_id`: 保代云服平台的账户标识
- `access_level`: 访问级别，影响可用功能
- `product_permissions`: 产品权限，限制可投保的产品类型
- `rate_discount`: 费率折扣，不同账户享受不同优惠

## 4. 数据处理逻辑

### 4.1 数据标准化

```php
protected function standardizeData($data) {
    $standardized = [];
    
    // 基础信息标准化
    $standardized['order_no'] = $this->formatOrderNumber($data['order_no']);
    $standardized['holder_name'] = trim($data['holder_name']);
    $standardized['holder_id'] = $this->formatIdNumber($data['holder_id']);
    $standardized['recognizee_name'] = trim($data['recognizee_name']);
    
    // 货物信息标准化
    $standardized['goods_name'] = $this->standardizeGoodsName($data['goods_name']);
    $standardized['goods_value'] = $this->formatAmount($data['insured_amount']);
    $standardized['premium'] = $this->formatAmount($data['premium']);
    
    // 运输信息标准化
    $standardized['departure_place'] = $this->standardizeLocation($data['from_loc']);
    $standardized['destination_place'] = $this->standardizeLocation($data['to_loc']);
    $standardized['transit_place'] = $this->standardizeLocation($data['via_loc'] ?? '');
    
    // 时间标准化
    $standardized['departure_time'] = $this->standardizeDateTime($data['departure_date']);
    
    return $standardized;
}
```

### 4.2 订单号格式化

```php
protected function formatOrderNumber($order_no) {
    // 保代云服要求订单号格式：BDYF + 日期 + 序号
    $prefix = 'BDYF';
    $date = date('Ymd');
    
    // 如果原订单号已经符合格式，直接返回
    if (strpos($order_no, $prefix) === 0) {
        return $order_no;
    }
    
    // 否则重新格式化
    $sequence = substr(md5($order_no), 0, 6);
    return $prefix . $date . $sequence;
}
```

### 4.3 货物名称标准化

```php
protected function standardizeGoodsName($goods_name) {
    // 保代云服货物名称映射表
    $mappings = [
        '电子产品' => '电子设备及配件',
        '服装' => '纺织品及服装',
        '食品' => '食品及农产品',
        '化工产品' => '化学工业产品',
        '机械设备' => '机械设备及零件',
        '汽车配件' => '汽车零部件',
        '建材' => '建筑材料',
        '日用品' => '日用消费品'
    ];
    
    foreach ($mappings as $key => $value) {
        if (strpos($goods_name, $key) !== false) {
            return $value;
        }
    }
    
    // 如果没有匹配，返回原名称
    return $goods_name;
}
```

### 4.4 地点标准化

```php
protected function standardizeLocation($location) {
    if (empty($location)) {
        return '';
    }
    
    // 移除常见的后缀
    $suffixes = ['市', '区', '县', '镇', '港', '机场', '站'];
    $standardized = $location;
    
    foreach ($suffixes as $suffix) {
        if (substr($standardized, -strlen($suffix)) === $suffix) {
            $standardized = substr($standardized, 0, -strlen($suffix));
            break;
        }
    }
    
    // 城市名称映射
    $city_mappings = [
        '北京' => '北京市',
        '上海' => '上海市',
        '广州' => '广州市',
        '深圳' => '深圳市',
        '天津' => '天津市',
        '重庆' => '重庆市'
    ];
    
    if (isset($city_mappings[$standardized])) {
        return $city_mappings[$standardized];
    }
    
    return $standardized;
}
```

## 5. 自动化录单实现

### 5.1 录单参数准备

```php
protected function prepareAutomationParams($data, $account) {
    $account_config = $this->getAccountConfig($account);
    
    return [
        'platform' => 'BDYF',
        'account_id' => $account_config['account_id'],
        'access_level' => $account_config['access_level'],
        'policy_data' => $data,
        'options' => [
            'auto_submit' => true,
            'wait_for_result' => true,
            'timeout' => 300, // 5分钟
            'screenshot' => true
        ]
    ];
}
```

### 5.2 自动化流程

1. **平台登录**: 使用账户信息登录保代云服平台
2. **产品选择**: 根据业务类型选择对应的保险产品
3. **信息录入**:
   - 投保人信息录入
   - 被保险人信息录入
   - 货物信息录入
   - 运输路线录入
   - 保险条款选择
4. **费率计算**: 系统自动计算保费
5. **数据确认**: 确认所有录入信息
6. **提交录单**: 提交到后端保险公司
7. **结果获取**: 获取录单结果和保单信息

### 5.3 Python脚本调用

```php
protected function executeAutomation($params) {
    $script_path = $this->getScriptPath() . '/bdyf_automation.py';
    
    $command = sprintf(
        'python %s --params %s --log-level INFO',
        escapeshellarg($script_path),
        escapeshellarg(json_encode($params))
    );
    
    $output = shell_exec($command);
    
    if ($output === null) {
        throw new Exception('BDYF automation script execution failed');
    }
    
    return $this->parseAutomationResult($output);
}
```

## 6. 错误处理

### 6.1 错误分类

```php
class BDYFErrorHandler {
    const ERROR_TYPES = [
        'ACCOUNT_ERROR' => '账户权限错误',
        'PRODUCT_ERROR' => '产品配置错误',
        'VALIDATION_ERROR' => '数据验证错误',
        'PLATFORM_ERROR' => '保代云服平台错误',
        'BACKEND_ERROR' => '后端保险公司错误',
        'NETWORK_ERROR' => '网络连接错误',
        'AUTOMATION_ERROR' => '自动化脚本错误'
    ];
    
    public function handleError($error_type, $message, $context = []) {
        switch ($error_type) {
            case 'NETWORK_ERROR':
            case 'PLATFORM_ERROR':
                // 平台相关错误，可重试
                return $this->createRetryableError($error_type, $message, $context);
            case 'ACCOUNT_ERROR':
                // 账户错误，需要检查权限
                return $this->createAccountError($error_type, $message, $context);
            case 'BACKEND_ERROR':
                // 后端保险公司错误，可能需要重试
                return $this->analyzeBackendError($message, $context);
            default:
                // 其他错误，一般不可重试
                return $this->createFinalError($error_type, $message, $context);
        }
    }
    
    protected function analyzeBackendError($message, $context) {
        // 分析后端错误类型
        if (strpos($message, '系统维护') !== false) {
            return $this->createRetryableError('BACKEND_MAINTENANCE', $message, $context);
        }
        
        if (strpos($message, '网络超时') !== false) {
            return $this->createRetryableError('BACKEND_TIMEOUT', $message, $context);
        }
        
        // 其他后端错误
        return $this->createFinalError('BACKEND_ERROR', $message, $context);
    }
}
```

### 6.2 账户权限检查

```php
protected function checkAccountPermissions($account, $product_type) {
    $account_config = $this->getAccountConfig($account);
    
    if (!isset($account_config['product_permissions'])) {
        throw new Exception('Account permissions not configured');
    }
    
    $permissions = $account_config['product_permissions'];
    
    if (!in_array($product_type, $permissions)) {
        throw new Exception(sprintf(
            'Account %s does not have permission for product %s',
            $account,
            $product_type
        ));
    }
    
    return true;
}
```

## 7. 监控和日志

### 7.1 关键指标

- **平台可用性**: 保代云服平台的可用性监控
- **录单成功率**: 按账户和产品类型统计的成功率
- **处理时间**: 录单处理的平均时间和分布
- **错误分析**: 各类错误的统计和趋势

### 7.2 日志记录

```php
// 开始处理
Log::info('BDYF processing started', [
    'order_no' => $data['order_no'],
    'account' => $account,
    'product_type' => $product_type,
    'goods_value' => $data['goods_value']
]);

// 账户权限检查
Log::debug('Account permission check', [
    'account' => $account,
    'access_level' => $account_config['access_level'],
    'product_permissions' => $account_config['product_permissions']
]);

// 自动化执行
Log::info('Automation execution', [
    'order_no' => $data['order_no'],
    'script_params' => $params,
    'execution_start' => microtime(true)
]);

// 处理完成
Log::info('BDYF processing completed', [
    'order_no' => $data['order_no'],
    'success' => $success,
    'policy_no' => $policy_no ?? null,
    'backend_company' => $backend_company ?? null,
    'processing_time' => $processing_time
]);
```

## 8. Go重构建议

### 8.1 适配器结构

```go
type BDYFAdapter struct {
    config     *BDYFConfig
    client     *http.Client
    logger     *zap.Logger
    accounts   map[string]*BDYFAccount
    automation *AutomationManager
}

type BDYFConfig struct {
    PlatformName     string                    `yaml:"platform_name"`
    PlatformCode     string                    `yaml:"platform_code"`
    APIVersion       string                    `yaml:"api_version"`
    SupportedProducts []string                 `yaml:"supported_products"`
    Accounts         map[string]*BDYFAccount   `yaml:"accounts"`
    DefaultSettings  map[string]string         `yaml:"default_settings"`
    GoodsMappings    map[string]string         `yaml:"goods_mappings"`
    CityMappings     map[string]string         `yaml:"city_mappings"`
}

type BDYFAccount struct {
    Description        string   `yaml:"description"`
    AccountID          string   `yaml:"account_id"`
    AccessLevel        string   `yaml:"access_level"`
    ProductPermissions []string `yaml:"product_permissions"`
    RateDiscount       float64  `yaml:"rate_discount"`
}
```

### 8.2 数据标准化器

```go
type DataStandardizer struct {
    config   *BDYFConfig
    logger   *zap.Logger
    mappings *MappingTables
}

type MappingTables struct {
    GoodsMappings map[string]string
    CityMappings  map[string]string
    CitySuffixes  []string
}

func (ds *DataStandardizer) StandardizeData(data *InsuranceData) (*StandardizedData, error) {
    standardized := &StandardizedData{
        OrderNo:         ds.formatOrderNumber(data.OrderNo),
        HolderName:      strings.TrimSpace(data.HolderName),
        HolderID:        ds.formatIDNumber(data.HolderID),
        RecognizeeName:  strings.TrimSpace(data.RecognizeeName),
        GoodsName:       ds.standardizeGoodsName(data.GoodsName),
        GoodsValue:      ds.formatAmount(data.InsuredAmount),
        Premium:         ds.formatAmount(data.Premium),
        DeparturePlace:  ds.standardizeLocation(data.FromLoc),
        DestinationPlace: ds.standardizeLocation(data.ToLoc),
        TransitPlace:    ds.standardizeLocation(data.ViaLoc),
        DepartureTime:   ds.standardizeDateTime(data.DepartureDate),
    }
    
    return standardized, nil
}
```

### 8.3 权限管理器

```go
type PermissionManager struct {
    accounts map[string]*BDYFAccount
    logger   *zap.Logger
}

func (pm *PermissionManager) CheckAccountPermissions(account, productType string) error {
    accountConfig, exists := pm.accounts[account]
    if !exists {
        return errors.New("account not found")
    }
    
    for _, permission := range accountConfig.ProductPermissions {
        if permission == productType {
            return nil
        }
    }
    
    return fmt.Errorf("account %s does not have permission for product %s", 
        account, productType)
}

func (pm *PermissionManager) GetRateDiscount(account string) (float64, error) {
    accountConfig, exists := pm.accounts[account]
    if !exists {
        return 0, errors.New("account not found")
    }
    
    return accountConfig.RateDiscount, nil
}
```

### 8.4 自动化管理器

```go
type AutomationManager struct {
    scriptPath string
    timeout    time.Duration
    logger     *zap.Logger
}

func (am *AutomationManager) ExecuteBDYFAutomation(ctx context.Context, params *AutomationParams) (*AutomationResult, error) {
    ctx, cancel := context.WithTimeout(ctx, am.timeout)
    defer cancel()
    
    paramsJSON, err := json.Marshal(params)
    if err != nil {
        return nil, err
    }
    
    cmd := exec.CommandContext(ctx, "python",
        filepath.Join(am.scriptPath, "bdyf_automation.py"),
        "--params", string(paramsJSON),
        "--log-level", "INFO")
    
    output, err := cmd.Output()
    if err != nil {
        return nil, fmt.Errorf("automation script failed: %w", err)
    }
    
    var result AutomationResult
    if err := json.Unmarshal(output, &result); err != nil {
        return nil, err
    }
    
    return &result, nil
}
```

这份文档详细说明了保代云服适配器的业务逻辑，包括平台特性、数据标准化、权限管理等核心功能。
