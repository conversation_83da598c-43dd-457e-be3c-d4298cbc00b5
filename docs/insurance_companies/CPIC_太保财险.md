# 太保财险 (CPIC) 适配器详细说明

## 1. 基本信息

- **保险公司**: 中国太平洋财产保险股份有限公司
- **适配器类型**: 自动化录单 (AUTO)
- **支持模式**: AUTO_CPIC, AUTO_CPIC_INTL
- **控制器**: CPICController.php
- **数据格式**: 网页表单提交

## 2. 业务流程

### 2.1 录单流程图

```mermaid
graph TD
    A[接收保单数据] --> B[账户配置验证]
    B --> C{判断账户类型}
    C -->|SHTB| D[上海太保配置]
    C -->|YRWL| E[优然物流配置]
    C -->|CDYF| F[成都优孚配置]
    D --> G[协议号处理]
    E --> G
    F --> H[特殊货物覆盖处理]
    H --> G
    G --> I[费率计算]
    I --> J[包装类型映射]
    J --> K[运输方式映射]
    K --> L[数据格式化]
    L --> M[Python自动化录单]
    M --> N{录单结果}
    N -->|成功| O[返回保单号]
    N -->|失败| P[返回错误信息]
```

### 2.2 数据处理流程

1. **数据接收**: 接收平台提交的保单数据
2. **账户识别**: 根据product配置识别使用的账户
3. **协议配置**: 根据账户加载对应的协议号和配置
4. **特殊处理**: CDYF账户需要特殊的货物覆盖配置
5. **数据转换**: 转换包装类型和运输方式
6. **费率计算**: 根据账户配置计算费率
7. **自动录单**: 调用Python脚本进行网页自动化录单

## 3. 账户配置详解

### 3.1 SHTB账户 (上海太保)

```json
{
  "description": "上海太保账户",
  "protocol_no": "A|3010200|C20180144-19001P000431|CAGHYX190032",
  "unit_code": "3010200",
  "franchise": "本保单其他承保条件同协议。",
  "rate_info": ["0.5", "3000000", "50"]
}
```

**配置说明**:
- `protocol_no`: 协议号，格式为 A|机构代码|协议编号|附加信息
- `unit_code`: 机构代码，用于标识承保机构
- `franchise`: 免赔条款，显示在保单上的免赔说明
- `rate_info`: [费率, 最高保额, 最低保费]

**业务特点**:
- 费率相对较高 (0.5‰)
- 保额上限高 (300万)
- 最低保费要求 (50元)

### 3.2 YRWL账户 (优然物流)

```json
{
  "description": "优然物流账户",
  "protocol_no": "A|3010100|C20190295|0",
  "unit_code": "3010100",
  "franchise": "本保单其他承保条件同协议; 每次事故绝对免赔额为人民币1000或损失金额的10%，两者取高;",
  "rate_info": ["0.15", "500000", "3"]
}
```

**配置说明**:
- 协议号格式相同，但机构代码不同
- 免赔条款更复杂，包含绝对免赔额计算规则
- 费率较低但保额限制也较低

**业务特点**:
- 费率低 (0.15‰)
- 保额上限中等 (50万)
- 最低保费低 (3元)
- 复杂的免赔额计算规则

### 3.3 CDYF账户 (成都优孚)

```json
{
  "description": "成都优孚账户",
  "protocol_no": "C|3010100|CSHHHYX2024P000473|0|CSHHHYX2024Q000452",
  "unit_code": "3010100",
  "holder_name": "成都优孚世纪信息技术有限公司",
  "special_processing": true,
  "goods_coverage_config": {
    "coverage_type": "special",
    "coverage_details": "特殊货物覆盖配置"
  }
}
```

**配置说明**:
- 协议号格式特殊，以C开头，包含两个协议编号
- 固定投保人名称
- 需要特殊的货物覆盖配置处理

**特殊处理逻辑**:
```php
if ($account == 'CDYF') {
    // 特殊的货物覆盖配置JSON处理
    $special_config = [
        'coverage_type' => 'comprehensive',
        'special_goods' => true,
        'additional_coverage' => $this->getCDYFCoverageConfig()
    ];
}
```

## 4. 数据映射规则

### 4.1 包装类型映射

| 平台包装类型 | 太保系统代码 | 说明 |
|-------------|-------------|------|
| 裸装 | 05 | 无包装货物 |
| 散装 | 04 | 散装货物 |
| 纸箱 | 01 | 纸箱包装 |
| 木箱 | 01 | 木箱包装 |
| 捆包 | 08 | 捆扎包装 |
| 袋装 | 02 | 袋装货物 |
| 篓装 | 01 | 篓装货物 |
| 托盘 | 03 | 托盘装载 |
| 桶装 | 06 | 桶装货物 |
| 罐装 | 07 | 罐装货物 |

### 4.2 运输方式映射

| 平台运输方式 | 太保系统代码 | 说明 |
|-------------|-------------|------|
| 3 (公路) | 1 | 公路运输 |
| 4 (铁路) | 5 | 铁路运输 |
| 5 (水路) | 4 | 水路运输 |
| 6 (航空) | 3 | 航空运输 |

## 5. 业务规则

### 5.1 费率计算规则

```php
// 费率转换：平台费率(万分之几) ÷ 10 = 太保费率(千分之几)
$insurance_rate = $platform_rate / 10;

// 保费计算
$premium = $insured_amount * $insurance_rate / 1000;

// 最低保费检查
if ($premium < $min_premium) {
    $premium = $min_premium;
}
```

### 5.2 时间处理规则

```php
// 起运时间调整
$departure_time = strtotime($departure_date);
$current_time = time();

// 如果是当天，延后1小时
if (date('Y-m-d', $departure_time) == date('Y-m-d', $current_time)) {
    $departure_time += 3600; // 延后1小时
}

// 如果当前分钟数超过50，再延后1小时
if (date('i', $current_time) > 50) {
    $departure_time += 3600;
}

// 如果超过24点，改为次日00:00
if (date('H', $departure_time) >= 24) {
    $departure_time = strtotime(date('Y-m-d', $departure_time) . ' +1 day');
}
```

### 5.3 发票号格式化

```php
// 根据平台类型格式化发票号
if ($platform_id == 5) { // 保呀平台
    $formatted_invoice = $inv_no . ' / ' . $freight_no;
} else { // 其他平台
    $formatted_invoice = $order_no . ' / ' . $inv_no;
}
```

## 6. 错误处理

### 6.1 常见错误类型

1. **账户配置错误**: 账户不存在或配置不完整
2. **协议号错误**: 协议号格式不正确或已过期
3. **费率计算错误**: 费率超出允许范围
4. **数据验证错误**: 必填字段缺失或格式不正确
5. **网络连接错误**: 太保系统连接超时或失败

### 6.2 错误处理策略

```php
try {
    // 录单处理
    $result = $this->processInsurance($data);
} catch (AccountConfigException $e) {
    // 账户配置错误
    return $this->errorResponse('ACCOUNT_CONFIG_ERROR', $e->getMessage());
} catch (RateCalculationException $e) {
    // 费率计算错误
    return $this->errorResponse('RATE_CALCULATION_ERROR', $e->getMessage());
} catch (NetworkException $e) {
    // 网络错误，可重试
    return $this->retryableErrorResponse('NETWORK_ERROR', $e->getMessage());
} catch (Exception $e) {
    // 其他错误
    return $this->errorResponse('UNKNOWN_ERROR', $e->getMessage());
}
```

## 7. 自动化录单实现

### 7.1 Python脚本调用

```php
// 构建Python脚本参数
$script_params = [
    'mode' => $mode,
    'account' => $account,
    'data' => json_encode($formatted_data),
    'config' => json_encode($account_config)
];

// 调用Python脚本
$command = sprintf(
    'python %s/cpic_automation.py %s',
    $script_path,
    escapeshellarg(json_encode($script_params))
);

$result = shell_exec($command);
```

### 7.2 自动化流程

1. **登录系统**: 使用账户信息登录太保录单系统
2. **选择产品**: 根据业务类型选择对应的保险产品
3. **填写信息**: 自动填写投保人、被保险人、货物等信息
4. **上传附件**: 如需要，上传相关附件文件
5. **提交录单**: 提交录单申请
6. **获取结果**: 获取录单结果和保单号
7. **下载保单**: 下载生成的保单文件

## 8. 监控和日志

### 8.1 关键指标监控

- **录单成功率**: 按账户统计的录单成功率
- **响应时间**: 录单处理的平均响应时间
- **错误率**: 各类错误的发生频率
- **重试次数**: 失败重试的统计

### 8.2 日志记录

```php
// 录单开始日志
Log::info('CPIC insurance processing started', [
    'order_no' => $order_no,
    'account' => $account,
    'mode' => $mode,
    'insured_amount' => $insured_amount
]);

// 录单结果日志
Log::info('CPIC insurance processing completed', [
    'order_no' => $order_no,
    'success' => $success,
    'policy_no' => $policy_no,
    'processing_time' => $processing_time
]);
```

## 9. Go重构建议

### 9.1 适配器接口设计

```go
type CPICAdapter struct {
    config *CPICConfig
    client *http.Client
    logger *zap.Logger
}

func (c *CPICAdapter) Process(ctx context.Context, data InsuranceData) (AdapterResponse, error) {
    // 1. 验证数据
    if err := c.Validate(data); err != nil {
        return AdapterResponse{}, err
    }
    
    // 2. 转换数据
    transformed, err := c.Transform(data)
    if err != nil {
        return AdapterResponse{}, err
    }
    
    // 3. 调用自动化脚本
    result, err := c.callAutomationScript(ctx, transformed)
    if err != nil {
        return AdapterResponse{}, err
    }
    
    return result, nil
}
```

### 9.2 配置管理

```go
type CPICConfig struct {
    Accounts map[string]CPICAccount `yaml:"accounts"`
    Mappings CPICMappings           `yaml:"mappings"`
    Scripts  CPICScripts            `yaml:"scripts"`
}

type CPICAccount struct {
    ProtocolNo string   `yaml:"protocol_no"`
    UnitCode   string   `yaml:"unit_code"`
    Franchise  string   `yaml:"franchise"`
    RateInfo   []string `yaml:"rate_info"`
    HolderName string   `yaml:"holder_name,omitempty"`
    Special    bool     `yaml:"special_processing,omitempty"`
}
```

### 9.3 错误处理

```go
type CPICError struct {
    Type    string `json:"type"`
    Code    string `json:"code"`
    Message string `json:"message"`
    Account string `json:"account,omitempty"`
}

func (e CPICError) Error() string {
    return fmt.Sprintf("CPIC Error [%s:%s] %s", e.Type, e.Code, e.Message)
}
```

这份文档详细说明了太保财险适配器的所有业务逻辑、配置规则和实现细节，为Go重构提供了完整的参考。
