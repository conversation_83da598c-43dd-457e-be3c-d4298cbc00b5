# 中意雇主 (GROUP_ZHONGYI) 适配器详细说明

## 1. 基本信息

- **保险公司**: 中意人寿保险有限公司
- **适配器类型**: API接口 (API)
- **支持模式**: API_GROUP_ZY
- **控制器**: GROUP_ZHONGYIController.php
- **数据格式**: JSON
- **业务类型**: 雇主责任险

## 2. 业务流程

### 2.1 API调用流程图

```mermaid
graph TD
    A[接收雇主责任险数据] --> B[雇员信息验证]
    B --> C[雇主信息处理]
    C --> D[保险方案配置]
    D --> E[保费计算]
    E --> F[JSON数据构建]
    F --> G[API请求发送]
    G --> H{响应状态}
    H -->|成功| I[解析JSON响应]
    H -->|失败| J[错误处理]
    I --> K[提取保单信息]
    K --> L[返回结果]
    J --> M[重试机制]
    M --> G
```

### 2.2 业务特点

1. **雇主责任险**: 专门针对雇主责任的保险产品
2. **雇员管理**: 需要管理雇员信息和保障范围
3. **动态保费**: 根据雇员数量和风险等级动态计算保费
4. **批量操作**: 支持批量添加和管理雇员

## 3. 业务配置

### 3.1 基础配置

```json
{
  "product_name": "雇主责任险",
  "product_code": "GROUP_ZY_001",
  "api_endpoint": "https://api.zhongyi.com/employer/v1",
  "api_version": "1.0",
  "default_settings": {
    "currency": "CNY",
    "coverage_period": "1年",
    "min_employees": 1,
    "max_employees": 1000,
    "default_coverage_amount": 500000
  }
}
```

**配置说明**:
- `product_code`: 中意雇主责任险产品代码
- `api_endpoint`: API接口地址
- `coverage_period`: 保障期间
- `min_employees/max_employees`: 雇员数量限制
- `default_coverage_amount`: 默认保障金额

### 3.2 风险等级配置

```json
{
  "risk_levels": {
    "low": {
      "description": "低风险行业",
      "industries": ["办公室工作", "咨询服务", "软件开发"],
      "base_rate": 0.002,
      "coverage_multiplier": 1.0
    },
    "medium": {
      "description": "中等风险行业",
      "industries": ["制造业", "零售业", "餐饮业"],
      "base_rate": 0.005,
      "coverage_multiplier": 1.2
    },
    "high": {
      "description": "高风险行业",
      "industries": ["建筑业", "化工业", "采矿业"],
      "base_rate": 0.010,
      "coverage_multiplier": 1.5
    }
  }
}
```

**配置说明**:
- `base_rate`: 基础费率
- `coverage_multiplier`: 保障倍数
- `industries`: 适用行业列表

## 4. 数据处理逻辑

### 4.1 雇主信息处理

```php
protected function processEmployerInfo($data) {
    $employer_info = [];
    
    // 基础信息
    $employer_info['company_name'] = $data['company_name'];
    $employer_info['company_id'] = $data['company_id'];
    $employer_info['legal_representative'] = $data['legal_representative'];
    $employer_info['contact_person'] = $data['contact_person'];
    $employer_info['contact_phone'] = $data['contact_phone'];
    $employer_info['contact_email'] = $data['contact_email'];
    
    // 公司信息
    $employer_info['industry'] = $data['industry'];
    $employer_info['business_scope'] = $data['business_scope'];
    $employer_info['company_address'] = $data['company_address'];
    $employer_info['registration_date'] = $data['registration_date'];
    
    // 风险评估
    $employer_info['risk_level'] = $this->assessRiskLevel($data['industry']);
    
    return $employer_info;
}
```

### 4.2 雇员信息处理

```php
protected function processEmployeeInfo($employees_data) {
    $employees = [];
    
    foreach ($employees_data as $employee) {
        $employee_info = [
            'employee_id' => $employee['id'],
            'name' => $employee['name'],
            'id_number' => $employee['id_number'],
            'gender' => $employee['gender'],
            'birth_date' => $employee['birth_date'],
            'hire_date' => $employee['hire_date'],
            'position' => $employee['position'],
            'department' => $employee['department'],
            'salary' => $employee['salary'],
            'work_location' => $employee['work_location'],
            'risk_category' => $this->categorizeEmployeeRisk($employee['position'])
        ];
        
        // 年龄计算
        $employee_info['age'] = $this->calculateAge($employee['birth_date']);
        
        // 工龄计算
        $employee_info['work_years'] = $this->calculateWorkYears($employee['hire_date']);
        
        $employees[] = $employee_info;
    }
    
    return $employees;
}
```

### 4.3 风险等级评估

```php
protected function assessRiskLevel($industry) {
    $risk_config = $this->getRiskLevelConfig();
    
    foreach ($risk_config as $level => $config) {
        if (in_array($industry, $config['industries'])) {
            return $level;
        }
    }
    
    // 默认中等风险
    return 'medium';
}
```

### 4.4 雇员风险分类

```php
protected function categorizeEmployeeRisk($position) {
    $high_risk_positions = [
        '电工', '焊工', '高空作业', '机械操作员', 
        '化学品处理员', '司机', '保安'
    ];
    
    $medium_risk_positions = [
        '销售员', '客服', '仓库管理员', '清洁工',
        '厨师', '服务员', '技术员'
    ];
    
    foreach ($high_risk_positions as $high_risk) {
        if (strpos($position, $high_risk) !== false) {
            return 'high';
        }
    }
    
    foreach ($medium_risk_positions as $medium_risk) {
        if (strpos($position, $medium_risk) !== false) {
            return 'medium';
        }
    }
    
    // 默认低风险（办公室工作）
    return 'low';
}
```

## 5. 保费计算

### 5.1 保费计算逻辑

```php
protected function calculatePremium($employer_info, $employees, $coverage_amount) {
    $risk_config = $this->getRiskLevelConfig();
    $employer_risk = $employer_info['risk_level'];
    $base_rate = $risk_config[$employer_risk]['base_rate'];
    
    $total_premium = 0;
    
    foreach ($employees as $employee) {
        // 基础保费
        $base_premium = $coverage_amount * $base_rate;
        
        // 年龄调整
        $age_factor = $this->getAgeFactor($employee['age']);
        
        // 职位风险调整
        $position_factor = $this->getPositionFactor($employee['risk_category']);
        
        // 工龄调整
        $experience_factor = $this->getExperienceFactor($employee['work_years']);
        
        // 计算个人保费
        $individual_premium = $base_premium * $age_factor * $position_factor * $experience_factor;
        
        $total_premium += $individual_premium;
    }
    
    // 团体折扣
    $group_discount = $this->getGroupDiscount(count($employees));
    $total_premium *= $group_discount;
    
    return round($total_premium, 2);
}
```

### 5.2 调整因子

```php
protected function getAgeFactor($age) {
    if ($age < 25) return 1.1;      // 年轻员工风险较高
    if ($age < 35) return 1.0;      // 标准费率
    if ($age < 50) return 0.95;     // 经验丰富，风险较低
    if ($age < 60) return 1.05;     // 年龄较大，风险上升
    return 1.2;                     // 高龄员工风险较高
}

protected function getPositionFactor($risk_category) {
    $factors = [
        'low' => 0.8,
        'medium' => 1.0,
        'high' => 1.5
    ];
    
    return $factors[$risk_category] ?? 1.0;
}

protected function getExperienceFactor($work_years) {
    if ($work_years < 1) return 1.2;   // 新员工风险较高
    if ($work_years < 3) return 1.1;   // 经验较少
    if ($work_years < 10) return 1.0;  // 标准费率
    return 0.9;                        // 经验丰富，风险较低
}

protected function getGroupDiscount($employee_count) {
    if ($employee_count >= 100) return 0.85;  // 15%折扣
    if ($employee_count >= 50) return 0.90;   // 10%折扣
    if ($employee_count >= 20) return 0.95;   // 5%折扣
    return 1.0;                               // 无折扣
}
```

## 6. JSON数据结构

### 6.1 请求JSON结构

```json
{
  "header": {
    "api_version": "1.0",
    "request_id": "REQ_20240101_001",
    "timestamp": "2024-01-01T12:00:00Z",
    "client_id": "YOUFU_CLIENT"
  },
  "body": {
    "policy_info": {
      "product_code": "GROUP_ZY_001",
      "coverage_period": "1年",
      "coverage_amount": 500000,
      "effective_date": "2024-01-01",
      "expire_date": "2024-12-31"
    },
    "employer_info": {
      "company_name": "上海优孚世纪信息技术有限公司",
      "company_id": "91310115MA1FL6LQ5X",
      "legal_representative": "张三",
      "contact_person": "李四",
      "contact_phone": "021-12345678",
      "contact_email": "<EMAIL>",
      "industry": "软件开发",
      "business_scope": "信息技术服务",
      "company_address": "上海市浦东新区张江高科技园区",
      "risk_level": "low"
    },
    "employees": [
      {
        "employee_id": "EMP001",
        "name": "王五",
        "id_number": "110101199001011234",
        "gender": "男",
        "birth_date": "1990-01-01",
        "hire_date": "2020-01-01",
        "position": "软件工程师",
        "department": "技术部",
        "salary": 15000,
        "work_location": "上海",
        "risk_category": "low",
        "age": 34,
        "work_years": 4
      }
    ],
    "premium_calculation": {
      "total_premium": 1250.00,
      "calculation_details": {
        "base_premium": 1000.00,
        "risk_adjustment": 1.0,
        "group_discount": 0.95,
        "final_premium": 950.00
      }
    }
  }
}
```

### 6.2 响应JSON结构

```json
{
  "header": {
    "result_code": "0000",
    "result_message": "成功",
    "timestamp": "2024-01-01T12:00:01Z",
    "response_id": "RESP_20240101_001"
  },
  "body": {
    "policy_result": {
      "success": true,
      "policy_no": "ZY202401010001",
      "certificate_no": "CERT202401010001",
      "effective_date": "2024-01-01T00:00:00Z",
      "expire_date": "2024-12-31T23:59:59Z",
      "premium_amount": 1250.00,
      "coverage_amount": 500000,
      "employee_count": 25,
      "policy_status": "active",
      "underwriting_result": {
        "status": "approved",
        "underwriter": "中意核保系统",
        "approval_date": "2024-01-01T12:00:01Z",
        "special_conditions": []
      },
      "error_info": null
    }
  }
}
```

## 7. API接口调用

### 7.1 HTTP请求处理

```php
protected function callZhongyiAPI($json_data) {
    $url = $this->getAPIEndpoint();
    
    $headers = [
        'Content-Type: application/json; charset=UTF-8',
        'Accept: application/json',
        'Authorization: Bearer ' . $this->getAccessToken(),
        'X-Client-ID: YOUFU_CLIENT',
        'X-API-Version: 1.0'
    ];
    
    $options = [
        'http' => [
            'method' => 'POST',
            'header' => implode("\r\n", $headers),
            'content' => $json_data,
            'timeout' => 60
        ],
        'ssl' => [
            'verify_peer' => true,
            'verify_peer_name' => true
        ]
    ];
    
    $context = stream_context_create($options);
    $response = file_get_contents($url, false, $context);
    
    if ($response === false) {
        throw new Exception('Zhongyi API request failed');
    }
    
    return $this->parseJSONResponse($response);
}
```

### 7.2 访问令牌管理

```php
protected function getAccessToken() {
    $cached_token = $this->getCachedToken();
    
    if ($cached_token && !$this->isTokenExpired($cached_token)) {
        return $cached_token['access_token'];
    }
    
    // 获取新的访问令牌
    $new_token = $this->requestNewToken();
    $this->cacheToken($new_token);
    
    return $new_token['access_token'];
}

protected function requestNewToken() {
    $auth_url = $this->getAuthEndpoint();
    $auth_data = [
        'client_id' => $this->getClientId(),
        'client_secret' => $this->getClientSecret(),
        'grant_type' => 'client_credentials',
        'scope' => 'employer_insurance'
    ];
    
    $response = $this->makeAuthRequest($auth_url, $auth_data);
    
    return json_decode($response, true);
}
```

## 8. 错误处理

### 8.1 错误类型

```php
class ZhongyiErrorHandler {
    const ERROR_CODES = [
        'ZY001' => '雇主信息验证失败',
        'ZY002' => '雇员信息不完整',
        'ZY003' => '风险等级评估失败',
        'ZY004' => '保费计算错误',
        'ZY005' => 'API认证失败',
        'ZY006' => '核保规则不通过',
        'ZY007' => '系统维护中',
        'ZY008' => '网络连接超时'
    ];
    
    public function handleZhongyiError($error_code, $error_message) {
        switch ($error_code) {
            case 'AUTH_FAILED':
                return $this->createAuthError('ZY005', 'API认证失败');
            case 'UNDERWRITING_REJECTED':
                return $this->createUnderwritingError('ZY006', '核保规则不通过: ' . $error_message);
            case 'SYSTEM_MAINTENANCE':
                return $this->createRetryableError('ZY007', '系统维护中');
            case 'NETWORK_TIMEOUT':
                return $this->createRetryableError('ZY008', '网络连接超时');
            default:
                return $this->createFinalError($error_code, $error_message);
        }
    }
}
```

## 9. Go重构建议

### 9.1 适配器结构

```go
type ZhongyiAdapter struct {
    config      *ZhongyiConfig
    httpClient  *http.Client
    logger      *zap.Logger
    tokenManager *TokenManager
    calculator  *PremiumCalculator
}

type ZhongyiConfig struct {
    ProductCode      string                 `yaml:"product_code"`
    APIEndpoint      string                 `yaml:"api_endpoint"`
    AuthEndpoint     string                 `yaml:"auth_endpoint"`
    ClientID         string                 `yaml:"client_id"`
    ClientSecret     string                 `yaml:"client_secret"`
    RiskLevels       map[string]*RiskLevel  `yaml:"risk_levels"`
    DefaultSettings  map[string]interface{} `yaml:"default_settings"`
}

type RiskLevel struct {
    Description        string   `yaml:"description"`
    Industries         []string `yaml:"industries"`
    BaseRate           float64  `yaml:"base_rate"`
    CoverageMultiplier float64  `yaml:"coverage_multiplier"`
}
```

### 9.2 保费计算器

```go
type PremiumCalculator struct {
    config *ZhongyiConfig
    logger *zap.Logger
}

func (pc *PremiumCalculator) CalculatePremium(employer *EmployerInfo, employees []*EmployeeInfo, coverageAmount float64) (*PremiumResult, error) {
    riskLevel := pc.assessRiskLevel(employer.Industry)
    baseRate := pc.config.RiskLevels[riskLevel].BaseRate
    
    var totalPremium float64
    
    for _, employee := range employees {
        basePremium := coverageAmount * baseRate
        
        ageFactor := pc.getAgeFactor(employee.Age)
        positionFactor := pc.getPositionFactor(employee.RiskCategory)
        experienceFactor := pc.getExperienceFactor(employee.WorkYears)
        
        individualPremium := basePremium * ageFactor * positionFactor * experienceFactor
        totalPremium += individualPremium
    }
    
    groupDiscount := pc.getGroupDiscount(len(employees))
    totalPremium *= groupDiscount
    
    return &PremiumResult{
        TotalPremium:    math.Round(totalPremium*100) / 100,
        BasePremium:     coverageAmount * baseRate,
        GroupDiscount:   groupDiscount,
        EmployeeCount:   len(employees),
        RiskLevel:       riskLevel,
    }, nil
}
```

### 9.3 令牌管理器

```go
type TokenManager struct {
    config     *ZhongyiConfig
    httpClient *http.Client
    logger     *zap.Logger
    cache      *TokenCache
}

type TokenCache struct {
    AccessToken string    `json:"access_token"`
    ExpiresAt   time.Time `json:"expires_at"`
    TokenType   string    `json:"token_type"`
}

func (tm *TokenManager) GetAccessToken(ctx context.Context) (string, error) {
    if tm.cache != nil && time.Now().Before(tm.cache.ExpiresAt) {
        return tm.cache.AccessToken, nil
    }
    
    token, err := tm.requestNewToken(ctx)
    if err != nil {
        return "", err
    }
    
    tm.cache = &TokenCache{
        AccessToken: token.AccessToken,
        ExpiresAt:   time.Now().Add(time.Duration(token.ExpiresIn) * time.Second),
        TokenType:   token.TokenType,
    }
    
    return token.AccessToken, nil
}
```

这份文档详细说明了中意雇主责任险适配器的业务逻辑，包括雇员管理、风险评估、保费计算等核心功能。
