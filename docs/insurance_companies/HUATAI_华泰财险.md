# 华泰财险 (HUATAI) 适配器详细说明

## 1. 基本信息

- **保险公司**: 华泰财产保险股份有限公司
- **适配器类型**: API接口 (API)
- **支持模式**: API_HUATAI
- **控制器**: HUATAIController.php
- **数据格式**: XML
- **接口类型**: HTTP POST

## 2. 业务流程

### 2.1 API调用流程图

```mermaid
graph TD
    A[接收保单数据] --> B[数据验证]
    B --> C[货物类型映射]
    C --> D[运输方式映射]
    D --> E[条款映射]
    E --> F[固定值设置]
    F --> G[XML数据构建]
    G --> H[API请求发送]
    H --> I{响应状态}
    I -->|成功| J[解析XML响应]
    I -->|失败| K[错误处理]
    J --> L[提取保单信息]
    L --> M[返回结果]
    K --> N[重试机制]
    N --> H
```

### 2.2 数据处理流程

1. **数据接收**: 接收标准化的保单数据
2. **映射转换**: 将平台数据映射为华泰系统格式
3. **XML构建**: 构建符合华泰API规范的XML请求
4. **API调用**: 发送HTTP POST请求到华泰接口
5. **响应解析**: 解析XML响应获取结果
6. **错误处理**: 处理各种异常情况

## 3. 数据映射规则

### 3.1 货物类型映射

| 货物类型 | 华泰代码 | 华泰描述 |
|----------|----------|----------|
| 纺织原料及纺织制品 | SX001411 | SX00140065 |
| 机器设备及其零件、附件 | SX001416 | SX00140087 |
| 食品 | SX001404 | SX00140019 |
| 化学工业及其相关工业产品 | SX001406 | SX00140040 |
| 塑料及其制品;橡胶及其制品 | SX001407 | SX00140041 |
| 玻璃及玻璃制品 | SX001413 | SX00140072 |
| 新车 | SX001417 | SX00140089 |
| 二手车 | SX001417 | SX00140089 |

### 3.2 运输方式映射

| 平台运输方式 | 车型 | 华泰运输代码 | 华泰车型代码 |
|-------------|------|-------------|-------------|
| 3 (公路) | 厢式货车 | SX001501 | 01 |
| 3 (公路) | 非厢式货车 | SX001501 | 05 |
| 4 (铁路) | 厢式货车 | SX001503 | 01 |
| 4 (铁路) | 非厢式货车 | SX001503 | 02 |
| 5 (水路) | 厢式货车 | SX001502 | 01 |
| 5 (水路) | 非厢式货车 | SX001502 | 02 |
| 6 (航空) | 厢式货车 | SX001505 | 01 |
| 6 (航空) | 非厢式货车 | SX001505 | 02 |

### 3.3 条款映射

| 平台条款 | 华泰条款代码 | 华泰条款名称 |
|----------|-------------|-------------|
| JBX | SX300211 | 基本险 |
| ZHX | SX300212 | 综合险 |

## 4. 固定配置值

### 4.1 系统固定值

```json
{
  "survey_address_id": "501422495713",
  "survey_address": "17/F,Block B,Center Plaza 161 Linhexi Av.,Tianhe District, Guangzhou TEL:4006095509 FAX: 020-87567201",
  "from_country": "HTC01",
  "to_country": "HTC01",
  "id_type": "99"
}
```

**配置说明**:
- `survey_address_id`: 查勘地址ID
- `survey_address`: 查勘地址详细信息
- `from_country`: 起运国家代码
- `to_country`: 到达国家代码
- `id_type`: 证件类型代码

## 5. XML数据结构

### 5.1 请求XML模板

```xml
<?xml version="1.0" encoding="UTF-8"?>
<request>
    <header>
        <version>1.0</version>
        <timestamp>{timestamp}</timestamp>
        <request_id>{request_id}</request_id>
    </header>
    <body>
        <policy_info>
            <order_no>{order_no}</order_no>
            <holder_name>{holder_name}</holder_name>
            <holder_id>{holder_id}</holder_id>
            <id_type>{id_type}</id_type>
            <recognizee_name>{recognizee_name}</recognizee_name>
            <goods_info>
                <goods_name>{goods_name}</goods_name>
                <goods_type_code>{goods_type_code}</goods_type_code>
                <goods_type_desc>{goods_type_desc}</goods_type_desc>
                <insured_amount>{insured_amount}</insured_amount>
            </goods_info>
            <transport_info>
                <transport_code>{transport_code}</transport_code>
                <vehicle_type>{vehicle_type}</vehicle_type>
                <from_location>{from_location}</from_location>
                <to_location>{to_location}</to_location>
                <via_location>{via_location}</via_location>
                <departure_date>{departure_date}</departure_date>
            </transport_info>
            <clause_info>
                <clause_code>{clause_code}</clause_code>
                <clause_name>{clause_name}</clause_name>
            </clause_info>
            <survey_info>
                <address_id>{survey_address_id}</address_id>
                <address>{survey_address}</address>
            </survey_info>
            <premium>{premium}</premium>
        </policy_info>
    </body>
</request>
```

### 5.2 响应XML结构

```xml
<?xml version="1.0" encoding="UTF-8"?>
<response>
    <header>
        <result_code>{result_code}</result_code>
        <result_message>{result_message}</result_message>
        <timestamp>{timestamp}</timestamp>
    </header>
    <body>
        <policy_result>
            <success>{success}</success>
            <policy_no>{policy_no}</policy_no>
            <apply_no>{apply_no}</apply_no>
            <error_code>{error_code}</error_code>
            <error_message>{error_message}</error_message>
        </policy_result>
    </body>
</response>
```

## 6. 业务逻辑实现

### 6.1 数据转换处理

```php
protected function transformData($data) {
    $transformed = [];
    
    // 基础信息
    $transformed['order_no'] = $data['order_no'];
    $transformed['holder_name'] = $data['holder_name'];
    $transformed['holder_id'] = $data['holder_id'];
    $transformed['id_type'] = $this->fixed_values['id_type'];
    $transformed['recognizee_name'] = $data['recognizee_name'];
    
    // 货物信息映射
    $goods_mapping = $this->getGoodsTypeMapping($data['goods_name']);
    $transformed['goods_name'] = $data['goods_name'];
    $transformed['goods_type_code'] = $goods_mapping[0];
    $transformed['goods_type_desc'] = $goods_mapping[1];
    $transformed['insured_amount'] = $data['insured_amount'];
    
    // 运输信息映射
    $transport_mapping = $this->getTransportMapping($data['transport'], $data['vehicle_type']);
    $transformed['transport_code'] = $transport_mapping[0];
    $transformed['vehicle_type'] = $transport_mapping[1];
    $transformed['from_location'] = $data['from_loc'];
    $transformed['to_location'] = $data['to_loc'];
    $transformed['via_location'] = $data['via_loc'] ?? '';
    $transformed['departure_date'] = date('Y-m-d H:i:s', $data['departure_date']);
    
    // 条款信息
    $clause_mapping = $this->getClauseMapping($data['clause_type']);
    $transformed['clause_code'] = $clause_mapping[0];
    $transformed['clause_name'] = $clause_mapping[1];
    
    // 查勘信息
    $transformed['survey_address_id'] = $this->fixed_values['survey_address_id'];
    $transformed['survey_address'] = $this->fixed_values['survey_address'];
    
    // 保费
    $transformed['premium'] = $data['premium'];
    
    return $transformed;
}
```

### 6.2 货物类型映射

```php
protected function getGoodsTypeMapping($goods_name) {
    $mappings = [
        '纺织原料及纺织制品' => ['SX001411', 'SX00140065'],
        '机器设备及其零件、附件' => ['SX001416', 'SX00140087'],
        '食品' => ['SX001404', 'SX00140019'],
        '化学工业及其相关工业产品' => ['SX001406', 'SX00140040'],
        '塑料及其制品;橡胶及其制品' => ['SX001407', 'SX00140041'],
        '玻璃及玻璃制品' => ['SX001413', 'SX00140072'],
        '新车' => ['SX001417', 'SX00140089'],
        '二手车' => ['SX001417', 'SX00140089']
    ];
    
    // 模糊匹配
    foreach ($mappings as $key => $value) {
        if (strpos($goods_name, $key) !== false) {
            return $value;
        }
    }
    
    // 默认值
    return ['SX001404', 'SX00140019']; // 默认为食品类
}
```

### 6.3 运输方式映射

```php
protected function getTransportMapping($transport_type, $vehicle_type = null) {
    $mappings = [
        '3' => [ // 公路
            '厢式货车' => ['SX001501', '01'],
            '非厢式货车' => ['SX001501', '05']
        ],
        '4' => [ // 铁路
            '厢式货车' => ['SX001503', '01'],
            '非厢式货车' => ['SX001503', '02']
        ],
        '5' => [ // 水路
            '厢式货车' => ['SX001502', '01'],
            '非厢式货车' => ['SX001502', '02']
        ],
        '6' => [ // 航空
            '厢式货车' => ['SX001505', '01'],
            '非厢式货车' => ['SX001505', '02']
        ]
    ];
    
    if (isset($mappings[$transport_type])) {
        $transport_mappings = $mappings[$transport_type];
        
        if ($vehicle_type && isset($transport_mappings[$vehicle_type])) {
            return $transport_mappings[$vehicle_type];
        }
        
        // 默认使用厢式货车
        return $transport_mappings['厢式货车'];
    }
    
    // 默认公路厢式货车
    return ['SX001501', '01'];
}
```

## 7. API接口调用

### 7.1 HTTP请求处理

```php
protected function callHuataiAPI($xml_data) {
    $url = $this->getAPIEndpoint();
    
    $options = [
        'http' => [
            'method' => 'POST',
            'header' => [
                'Content-Type: application/xml; charset=UTF-8',
                'Content-Length: ' . strlen($xml_data)
            ],
            'content' => $xml_data,
            'timeout' => 30
        ]
    ];
    
    $context = stream_context_create($options);
    $response = file_get_contents($url, false, $context);
    
    if ($response === false) {
        throw new Exception('API request failed');
    }
    
    return $this->parseXMLResponse($response);
}
```

### 7.2 XML构建

```php
protected function buildXMLRequest($data) {
    $xml = new SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><request></request>');
    
    // 头部信息
    $header = $xml->addChild('header');
    $header->addChild('version', '1.0');
    $header->addChild('timestamp', date('Y-m-d H:i:s'));
    $header->addChild('request_id', $this->generateRequestId());
    
    // 主体信息
    $body = $xml->addChild('body');
    $policy_info = $body->addChild('policy_info');
    
    // 基础信息
    $policy_info->addChild('order_no', htmlspecialchars($data['order_no']));
    $policy_info->addChild('holder_name', htmlspecialchars($data['holder_name']));
    $policy_info->addChild('holder_id', $data['holder_id']);
    $policy_info->addChild('id_type', $data['id_type']);
    $policy_info->addChild('recognizee_name', htmlspecialchars($data['recognizee_name']));
    
    // 货物信息
    $goods_info = $policy_info->addChild('goods_info');
    $goods_info->addChild('goods_name', htmlspecialchars($data['goods_name']));
    $goods_info->addChild('goods_type_code', $data['goods_type_code']);
    $goods_info->addChild('goods_type_desc', $data['goods_type_desc']);
    $goods_info->addChild('insured_amount', $data['insured_amount']);
    
    // 运输信息
    $transport_info = $policy_info->addChild('transport_info');
    $transport_info->addChild('transport_code', $data['transport_code']);
    $transport_info->addChild('vehicle_type', $data['vehicle_type']);
    $transport_info->addChild('from_location', htmlspecialchars($data['from_location']));
    $transport_info->addChild('to_location', htmlspecialchars($data['to_location']));
    $transport_info->addChild('via_location', htmlspecialchars($data['via_location']));
    $transport_info->addChild('departure_date', $data['departure_date']);
    
    // 条款信息
    $clause_info = $policy_info->addChild('clause_info');
    $clause_info->addChild('clause_code', $data['clause_code']);
    $clause_info->addChild('clause_name', $data['clause_name']);
    
    // 查勘信息
    $survey_info = $policy_info->addChild('survey_info');
    $survey_info->addChild('address_id', $data['survey_address_id']);
    $survey_info->addChild('address', htmlspecialchars($data['survey_address']));
    
    // 保费
    $policy_info->addChild('premium', $data['premium']);
    
    return $xml->asXML();
}
```

### 7.3 响应解析

```php
protected function parseXMLResponse($xml_response) {
    $xml = simplexml_load_string($xml_response);
    
    if ($xml === false) {
        throw new Exception('Invalid XML response');
    }
    
    $result = [
        'success' => false,
        'policy_no' => null,
        'apply_no' => null,
        'error_code' => null,
        'error_message' => null
    ];
    
    // 解析头部
    $header = $xml->header;
    $result_code = (string)$header->result_code;
    $result_message = (string)$header->result_message;
    
    // 解析主体
    if (isset($xml->body->policy_result)) {
        $policy_result = $xml->body->policy_result;
        
        $result['success'] = ((string)$policy_result->success === 'true');
        $result['policy_no'] = (string)$policy_result->policy_no;
        $result['apply_no'] = (string)$policy_result->apply_no;
        $result['error_code'] = (string)$policy_result->error_code;
        $result['error_message'] = (string)$policy_result->error_message;
    }
    
    return $result;
}
```

## 8. 错误处理

### 8.1 错误类型

```php
class HuataiErrorHandler {
    const ERROR_CODES = [
        'H001' => '货物类型映射失败',
        'H002' => '运输方式映射失败',
        'H003' => 'XML构建失败',
        'H004' => 'API调用失败',
        'H005' => 'XML解析失败',
        'H006' => '网络连接超时',
        'H007' => '华泰系统错误'
    ];
    
    public function handleAPIError($error_code, $error_message) {
        switch ($error_code) {
            case 'NETWORK_TIMEOUT':
                return $this->createRetryableError('H006', '网络连接超时');
            case 'SYSTEM_ERROR':
                return $this->createRetryableError('H007', '华泰系统错误: ' . $error_message);
            default:
                return $this->createFinalError($error_code, $error_message);
        }
    }
}
```

## 9. Go重构建议

### 9.1 适配器结构

```go
type HuataiAdapter struct {
    config     *HuataiConfig
    httpClient *http.Client
    logger     *zap.Logger
    mappings   *HuataiMappings
}

type HuataiConfig struct {
    APIEndpoint    string            `yaml:"api_endpoint"`
    Timeout        time.Duration     `yaml:"timeout"`
    FixedValues    map[string]string `yaml:"fixed_values"`
    GoodsMappings  map[string][]string `yaml:"goods_mappings"`
    TransportMappings map[string]map[string][]string `yaml:"transport_mappings"`
    ClauseMappings map[string][]string `yaml:"clause_mappings"`
}
```

### 9.2 XML处理

```go
type HuataiRequest struct {
    XMLName xml.Name `xml:"request"`
    Header  RequestHeader `xml:"header"`
    Body    RequestBody   `xml:"body"`
}

type RequestHeader struct {
    Version   string `xml:"version"`
    Timestamp string `xml:"timestamp"`
    RequestID string `xml:"request_id"`
}

type RequestBody struct {
    PolicyInfo PolicyInfo `xml:"policy_info"`
}

func (h *HuataiAdapter) buildXMLRequest(data *InsuranceData) (*HuataiRequest, error) {
    request := &HuataiRequest{
        Header: RequestHeader{
            Version:   "1.0",
            Timestamp: time.Now().Format("2006-01-02 15:04:05"),
            RequestID: h.generateRequestID(),
        },
        Body: RequestBody{
            PolicyInfo: h.transformToPolicyInfo(data),
        },
    }
    
    return request, nil
}
```

这份文档详细说明了华泰财险API接口的所有技术细节，包括XML数据结构、映射规则和错误处理机制。
