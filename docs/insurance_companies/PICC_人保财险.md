# 人保财险 (PICC) 适配器详细说明

## 1. 基本信息

- **保险公司**: 中国人民财产保险股份有限公司
- **适配器类型**: 自动化录单 (AUTO)
- **支持模式**: AUTO_PICC, AUTO_PICC_INTL
- **控制器**: PICCController.php
- **数据格式**: 网页表单提交

## 2. 业务流程

### 2.1 录单流程图

```mermaid
graph TD
    A[接收保单数据] --> B{判断模式}
    B -->|AUTO_PICC| C[国内业务处理]
    B -->|AUTO_PICC_INTL| D[国际业务处理]
    C --> E[账户配置加载]
    D --> E
    E --> F[数据标准化]
    F --> G[业务规则应用]
    G --> H[数据格式化]
    H --> I[Python自动化录单]
    I --> J{录单结果}
    J -->|成功| K[返回保单号]
    J -->|失败| L[错误处理]
    L --> M{是否可重试}
    M -->|是| I
    M -->|否| N[返回失败]
```

### 2.2 模式差异

| 模式 | 说明 | 特殊处理 |
|------|------|----------|
| AUTO_PICC | 国内货运险 | 标准处理流程 |
| AUTO_PICC_INTL | 国际货运险 | 国际业务规则，可能涉及外币 |

## 3. 账户配置

### 3.1 SHTB账户配置

```json
{
  "description": "上海人保账户",
  "account_type": "standard",
  "business_scope": ["domestic", "international"],
  "default_settings": {
    "currency": "CNY",
    "rate_type": "standard",
    "clause_type": "comprehensive"
  }
}
```

**配置说明**:
- `account_type`: 账户类型，标准账户
- `business_scope`: 业务范围，支持国内外业务
- `default_settings`: 默认设置，包括币种、费率类型、条款类型

## 4. 业务规则

### 4.1 数据处理规则

```php
protected function processData($data, $mode) {
    $processed = [];
    
    // 基础信息处理
    $processed['order_no'] = $data['order_no'];
    $processed['holder_name'] = $data['holder_name'];
    $processed['holder_id'] = $data['holder_id'];
    $processed['recognizee_name'] = $data['recognizee_name'];
    
    // 货物信息处理
    $processed['goods_name'] = $data['goods_name'];
    $processed['insured_amount'] = $this->formatAmount($data['insured_amount']);
    $processed['premium'] = $this->formatAmount($data['premium']);
    
    // 运输信息处理
    $processed['from_loc'] = $data['from_loc'];
    $processed['to_loc'] = $data['to_loc'];
    $processed['via_loc'] = $data['via_loc'] ?? '';
    $processed['transport'] = $data['transport'] ?? '';
    
    // 时间处理
    $processed['departure_date'] = $this->adjustDepartureTime($data['departure_date']);
    
    // 模式特殊处理
    if ($mode === 'AUTO_PICC_INTL') {
        $processed = $this->applyInternationalRules($processed);
    }
    
    return $processed;
}
```

### 4.2 国际业务特殊规则

```php
protected function applyInternationalRules($data) {
    // 币种处理
    if (!isset($data['currency'])) {
        $data['currency'] = 'USD'; // 国际业务默认美元
    }
    
    // 汇率处理
    if ($data['currency'] !== 'CNY') {
        $data['exchange_rate'] = $this->getCurrentExchangeRate($data['currency']);
        $data['cny_amount'] = $data['insured_amount'] * $data['exchange_rate'];
    }
    
    // 国际运输条款
    $data['international_terms'] = $this->getInternationalTerms();
    
    // 海关编码
    if (isset($data['customs_code'])) {
        $data['hs_code'] = $data['customs_code'];
    }
    
    return $data;
}
```

### 4.3 金额格式化

```php
protected function formatAmount($amount) {
    // 确保金额为数字
    $amount = floatval($amount);
    
    // 保留2位小数
    return sprintf('%.2f', $amount);
}
```

### 4.4 时间调整规则

```php
protected function adjustDepartureTime($departure_date) {
    $departure_time = is_numeric($departure_date) ? $departure_date : strtotime($departure_date);
    $current_time = time();
    
    // 如果是当天，延后1小时
    if (date('Y-m-d', $departure_time) == date('Y-m-d', $current_time)) {
        $departure_time += 3600;
    }
    
    // 如果当前分钟数超过50，再延后1小时
    if (date('i', $current_time) > 50) {
        $departure_time += 3600;
    }
    
    // 如果超过24点，改为次日00:00
    if (date('H', $departure_time) >= 24) {
        $departure_time = strtotime(date('Y-m-d', $departure_time) . ' +1 day');
    }
    
    return date('Y-m-d H:i:s', $departure_time);
}
```

## 5. 自动化录单实现

### 5.1 Python脚本调用

```php
protected function callAutomationScript($data, $mode) {
    $script_params = [
        'mode' => $mode,
        'url' => $this->getPICCUrl($mode),
        'login_info' => $this->getLoginInfo(),
        'policy_data' => $data,
        'timeout' => 600, // 10分钟超时
        'retry_count' => 3
    ];
    
    $command = sprintf(
        'python %s/picc_automation.py %s',
        $this->getScriptPath(),
        escapeshellarg(json_encode($script_params))
    );
    
    $result = shell_exec($command);
    
    return $this->parseScriptResult($result);
}
```

### 5.2 自动化流程

1. **环境准备**: 启动浏览器，设置用户代理和超时
2. **系统登录**: 使用账户信息登录人保录单系统
3. **业务选择**: 根据模式选择国内或国际业务
4. **信息录入**:
   - 投保人信息录入
   - 被保险人信息录入
   - 货物信息录入
   - 运输信息录入
   - 保险条款选择
5. **数据校验**: 系统自动校验录入数据
6. **提交处理**: 提交录单申请
7. **结果获取**: 获取录单结果和保单号
8. **异常处理**: 处理录单过程中的各种异常

### 5.3 脚本参数说明

```python
def process_picc_insurance(params):
    """
    处理人保财险录单
    
    Args:
        params: {
            'mode': 录单模式 (AUTO_PICC/AUTO_PICC_INTL),
            'url': 人保系统URL,
            'login_info': 登录信息,
            'policy_data': 保单数据,
            'timeout': 超时时间,
            'retry_count': 重试次数
        }
    
    Returns:
        {
            'success': 是否成功,
            'policy_no': 保单号,
            'apply_no': 投保单号,
            'error_message': 错误信息
        }
    """
    pass
```

## 6. 错误处理

### 6.1 错误分类

```php
class PICCErrorHandler {
    const ERROR_TYPES = [
        'VALIDATION_ERROR' => '数据验证错误',
        'LOGIN_ERROR' => '登录失败',
        'SYSTEM_ERROR' => '人保系统错误',
        'NETWORK_ERROR' => '网络连接错误',
        'TIMEOUT_ERROR' => '处理超时',
        'AUTOMATION_ERROR' => '自动化脚本错误'
    ];
    
    public function handleError($error_type, $message, $data = []) {
        switch ($error_type) {
            case 'NETWORK_ERROR':
            case 'TIMEOUT_ERROR':
            case 'SYSTEM_ERROR':
                // 可重试错误
                return $this->createRetryableError($error_type, $message, $data);
            case 'LOGIN_ERROR':
                // 登录错误，需要检查账户
                return $this->createAccountError($error_type, $message, $data);
            default:
                // 不可重试错误
                return $this->createFinalError($error_type, $message, $data);
        }
    }
}
```

### 6.2 重试机制

```php
protected function processWithRetry($data, $mode, $max_retries = 3) {
    $attempt = 0;
    $last_error = null;
    
    while ($attempt < $max_retries) {
        try {
            $result = $this->callAutomationScript($data, $mode);
            
            if ($result['success']) {
                return $result;
            }
            
            // 检查是否可重试
            if (!$this->isRetryableError($result['error_type'])) {
                break;
            }
            
            $last_error = $result;
            
        } catch (Exception $e) {
            $last_error = [
                'success' => false,
                'error_type' => 'EXCEPTION',
                'error_message' => $e->getMessage()
            ];
            
            if (!$this->isRetryableException($e)) {
                break;
            }
        }
        
        $attempt++;
        
        // 指数退避
        if ($attempt < $max_retries) {
            sleep(pow(2, $attempt));
        }
    }
    
    return $last_error ?? ['success' => false, 'error_message' => 'Unknown error'];
}
```

## 7. 监控和日志

### 7.1 关键指标

- **录单成功率**: 按模式统计的录单成功率
- **处理时间**: 录单处理的平均时间
- **错误分布**: 各类错误的分布情况
- **重试统计**: 重试次数和成功率

### 7.2 日志记录

```php
// 开始处理
Log::info('PICC processing started', [
    'order_no' => $data['order_no'],
    'mode' => $mode,
    'insured_amount' => $data['insured_amount'],
    'currency' => $data['currency'] ?? 'CNY'
]);

// 国际业务特殊日志
if ($mode === 'AUTO_PICC_INTL') {
    Log::debug('International business processing', [
        'order_no' => $data['order_no'],
        'currency' => $data['currency'],
        'exchange_rate' => $data['exchange_rate'] ?? null,
        'hs_code' => $data['hs_code'] ?? null
    ]);
}

// 处理完成
Log::info('PICC processing completed', [
    'order_no' => $data['order_no'],
    'success' => $success,
    'policy_no' => $policy_no ?? null,
    'processing_time' => $processing_time,
    'retry_count' => $retry_count
]);
```

## 8. Go重构建议

### 8.1 适配器结构

```go
type PICCAdapter struct {
    config   *PICCConfig
    client   *http.Client
    logger   *zap.Logger
    accounts map[string]*PICCAccount
}

type PICCConfig struct {
    Accounts    map[string]*PICCAccount `yaml:"accounts"`
    Scripts     *ScriptConfig           `yaml:"scripts"`
    Timeouts    map[string]time.Duration `yaml:"timeouts"`
    ExchangeAPI string                   `yaml:"exchange_api"`
}

type PICCAccount struct {
    Description   string            `yaml:"description"`
    AccountType   string            `yaml:"account_type"`
    BusinessScope []string          `yaml:"business_scope"`
    DefaultSettings map[string]string `yaml:"default_settings"`
}
```

### 8.2 模式处理器

```go
type ModeProcessor interface {
    Process(ctx context.Context, data *InsuranceData) (*ProcessResult, error)
    GetMode() string
    Validate(data *InsuranceData) error
}

type DomesticProcessor struct {
    config *PICCConfig
    logger *zap.Logger
}

type InternationalProcessor struct {
    config      *PICCConfig
    logger      *zap.Logger
    exchangeAPI *ExchangeRateAPI
}

func (p *InternationalProcessor) Process(ctx context.Context, data *InsuranceData) (*ProcessResult, error) {
    // 应用国际业务规则
    if err := p.applyInternationalRules(data); err != nil {
        return nil, err
    }
    
    // 处理汇率
    if data.Currency != "CNY" {
        rate, err := p.exchangeAPI.GetRate(data.Currency, "CNY")
        if err != nil {
            return nil, err
        }
        data.ExchangeRate = rate
        data.CNYAmount = data.InsuredAmount * rate
    }
    
    return p.processInsurance(ctx, data)
}
```

### 8.3 汇率服务

```go
type ExchangeRateAPI struct {
    client  *http.Client
    baseURL string
    apiKey  string
    logger  *zap.Logger
}

func (e *ExchangeRateAPI) GetRate(from, to string) (float64, error) {
    url := fmt.Sprintf("%s/latest?access_key=%s&symbols=%s,%s", 
        e.baseURL, e.apiKey, from, to)
    
    resp, err := e.client.Get(url)
    if err != nil {
        return 0, err
    }
    defer resp.Body.Close()
    
    var result ExchangeRateResponse
    if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
        return 0, err
    }
    
    if !result.Success {
        return 0, errors.New("exchange rate API error")
    }
    
    fromRate := result.Rates[from]
    toRate := result.Rates[to]
    
    return toRate / fromRate, nil
}
```

### 8.4 自动化脚本管理

```go
type AutomationScriptManager struct {
    scriptPath string
    timeout    time.Duration
    logger     *zap.Logger
}

func (asm *AutomationScriptManager) ExecutePICCScript(ctx context.Context, params *ScriptParams) (*ScriptResult, error) {
    ctx, cancel := context.WithTimeout(ctx, asm.timeout)
    defer cancel()
    
    paramsJSON, err := json.Marshal(params)
    if err != nil {
        return nil, err
    }
    
    cmd := exec.CommandContext(ctx, "python", 
        filepath.Join(asm.scriptPath, "picc_automation.py"),
        string(paramsJSON))
    
    output, err := cmd.Output()
    if err != nil {
        return nil, err
    }
    
    var result ScriptResult
    if err := json.Unmarshal(output, &result); err != nil {
        return nil, err
    }
    
    return &result, nil
}
```

这份文档详细说明了人保财险适配器的业务逻辑，包括国内外业务差异、汇率处理、自动化录单等核心功能。
