# 平安财险 (PINGAN) 适配器详细说明

## 1. 基本信息

**保险公司**: 中国平安财产保险股份有限公司
**适配器类型**: 自动化录单 (AUTO)
**支持模式**: AUTO_PINGAN, AUTO_PINGAN_INTL, AUTO_PINGAN_CBEC
**控制器**: PINGANController.php
**数据格式**: 网页表单提交

## 2. 业务流程概述

### 2.1 录单处理流程

平安财险的录单流程采用自动化网页操作方式，通过 Python 脚本模拟用户在平安录单系统中的操作。整个流程包括以下关键步骤：

1. **模式识别**: 系统首先根据传入的模式参数判断是国内业务、国际业务还是跨境电商业务
2. **账户配置**: 根据不同的账户加载对应的配置信息，包括投保人信息和特殊处理规则
3. **数据验证**: 对投保人身份信息进行严格验证，特别是身份证号码的格式和校验码验证
4. **信息处理**: 将标准化的保单数据转换为平安系统要求的格式
5. **自动化录单**: 通过 Python 脚本在平安录单系统中自动填写表单并提交
6. **结果处理**: 获取录单结果，成功则返回保单号，失败则进行错误分析和重试

### 2.2 三种业务模式的差异

**AUTO_PINGAN (国内货运险)**:

- 标准的国内货物运输保险业务
- 使用标准的处理流程和超时设置
- 支持常规的货物类型和运输方式

**AUTO_PINGAN_INTL (国际货运险)**:

- 针对国际货物运输的保险业务
- 需要处理更复杂的国际贸易条款
- 可能涉及外币和汇率转换

**AUTO_PINGAN_CBEC (跨境电商)**:

- 专门为跨境电商业务设计的保险产品
- 具有特殊的超时设置（2 小时失败检测时间）
- 处理流程相对复杂，需要更长的处理时间

## 3. 账户配置详解

### 3.1 SHTB 账户 (上海太保)

```json
{
  "description": "上海平安账户",
  "holder_name": "上海太保货运代理有限公司",
  "holder_id": "91310115MA1FL6LQ5X",
  "id_type": "business_license"
}
```

**配置说明**:

- `holder_name`: 固定投保人名称
- `holder_id`: 营业执照号码
- `id_type`: 证件类型标识

### 3.2 YRWL 账户 (优然物流)

```json
{
  "description": "优然物流账户",
  "holder_name": "上海优然物流有限公司",
  "holder_id": "91310115MA1FL6LQ5X",
  "id_type": "business_license"
}
```

### 3.3 CDYF 账户 (成都优孚)

```json
{
  "description": "成都优孚账户",
  "holder_name": "成都优孚世纪信息技术有限公司",
  "holder_id": "91510100MA61R8LN8H",
  "id_type": "business_license"
}
```

## 4. 数据处理逻辑

### 4.1 投保人信息处理

```php
protected function prepareHolderInfo($account, $data) {
    $account_config = $this->getAccountConfig($account);

    if (isset($account_config['holder_name'])) {
        // 使用固定投保人信息
        return [
            'holder_name' => $account_config['holder_name'],
            'holder_id' => $account_config['holder_id'],
            'id_type' => $account_config['id_type']
        ];
    }

    // 使用传入的投保人信息
    return [
        'holder_name' => $data['holder_name'],
        'holder_id' => $data['holder_id'],
        'id_type' => $this->determineIdType($data['holder_id'])
    ];
}
```

### 4.2 证件类型识别

```php
protected function determineIdType($id_number) {
    if ($this->isIdCard($id_number)) {
        return 'id_card';
    } elseif ($this->isBusinessLicense($id_number)) {
        return 'business_license';
    } elseif ($this->isPassport($id_number)) {
        return 'passport';
    } else {
        return 'other';
    }
}
```

### 4.3 身份证号验证

```php
protected function isIdCard($code) {
    // 18位身份证号验证
    if (strlen($code) == 18) {
        if (!preg_match('/^\d{17}[\dXx]$/', $code)) {
            return false;
        }

        // 校验码验证
        $factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
        $verifyCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];

        $sum = 0;
        for ($i = 0; $i < 17; $i++) {
            $sum += intval($code[$i]) * $factor[$i];
        }

        $mod = $sum % 11;
        $verifyCode = strtoupper($code[17]);

        return $verifyCode === $verifyCodes[$mod];
    }

    // 15位身份证号验证
    if (strlen($code) == 15) {
        return preg_match('/^\d{15}$/', $code);
    }

    return false;
}
```

## 5. 业务规则

### 5.1 失败检测超时规则

```php
protected function getFailureDetectionTimeout($mode) {
    switch ($mode) {
        case 'AUTO_PINGAN_CBEC':
            return 2 * 3600; // 2小时
        default:
            return 10 * 60;   // 10分钟
    }
}
```

### 5.2 客户数据准备

```php
protected function prepareClientData($data, $account) {
    $client_data = [];

    // 基础信息
    $client_data['order_no'] = $data['order_no'];
    $client_data['goods_name'] = $data['goods_name'];
    $client_data['insured_amount'] = $data['insured_amount'];
    $client_data['premium'] = $data['premium'];

    // 投保人信息
    $holder_info = $this->prepareHolderInfo($account, $data);
    $client_data = array_merge($client_data, $holder_info);

    // 被保险人信息
    $client_data['recognizee_name'] = $data['recognizee_name'];

    // 运输信息
    $client_data['from_loc'] = $data['from_loc'];
    $client_data['to_loc'] = $data['to_loc'];
    $client_data['via_loc'] = $data['via_loc'] ?? '';
    $client_data['transport'] = $data['transport'] ?? '';

    // 时间处理
    $client_data['departure_date'] = $this->adjustDepartureTime($data['departure_date']);

    // 发票信息
    $client_data['inv_no'] = $this->formatInvoiceNumber($data, $account);

    return $client_data;
}
```

### 5.3 发票号格式化

```php
protected function formatInvoiceNumber($data, $account) {
    $platform_id = $data['platform_id'];

    if ($platform_id == 5) { // 保呀平台
        return $data['inv_no'] . ' / ' . ($data['freight_no'] ?? '');
    } else { // 其他平台
        return $data['order_no'] . ' / ' . ($data['inv_no'] ?? '');
    }
}
```

## 6. 错误处理和重试机制

### 6.1 错误分类

```php
class PinganErrorHandler {
    const ERROR_TYPES = [
        'VALIDATION_ERROR' => '数据验证错误',
        'ACCOUNT_ERROR' => '账户配置错误',
        'ID_VALIDATION_ERROR' => '身份证验证错误',
        'NETWORK_ERROR' => '网络连接错误',
        'AUTOMATION_ERROR' => '自动化脚本错误',
        'TIMEOUT_ERROR' => '处理超时错误'
    ];

    public function handleError($error_type, $message, $data = []) {
        switch ($error_type) {
            case 'NETWORK_ERROR':
            case 'TIMEOUT_ERROR':
                // 可重试错误
                return $this->createRetryableError($error_type, $message, $data);
            default:
                // 不可重试错误
                return $this->createFinalError($error_type, $message, $data);
        }
    }
}
```

### 6.2 重试策略

```php
protected function processWithRetry($data, $max_retries = 3) {
    $attempt = 0;

    while ($attempt < $max_retries) {
        try {
            $result = $this->processInsurance($data);

            if ($result['success']) {
                return $result;
            }

            // 检查是否可重试
            if (!$this->isRetryableError($result['error'])) {
                break;
            }

        } catch (Exception $e) {
            if (!$this->isRetryableException($e)) {
                throw $e;
            }
        }

        $attempt++;

        // 指数退避
        if ($attempt < $max_retries) {
            sleep(pow(2, $attempt));
        }
    }

    return ['success' => false, 'error' => 'Max retries exceeded'];
}
```

## 7. 自动化录单实现

### 7.1 Python 脚本参数

```php
protected function buildScriptParams($data, $account, $mode) {
    return [
        'mode' => $mode,
        'account' => $account,
        'url' => $this->getPinganUrl($mode),
        'login_info' => $this->getLoginInfo($account),
        'client_data' => $data,
        'timeout' => $this->getFailureDetectionTimeout($mode),
        'retry_count' => 3
    ];
}
```

### 7.2 自动化流程

1. **环境准备**: 启动浏览器，设置用户代理
2. **系统登录**: 使用账户信息登录平安录单系统
3. **业务选择**: 根据模式选择对应的业务类型
4. **信息录入**:
   - 投保人信息录入和验证
   - 被保险人信息录入
   - 货物信息录入
   - 运输信息录入
5. **数据校验**: 系统自动校验录入数据
6. **提交处理**: 提交录单申请
7. **结果获取**: 获取录单结果和保单号
8. **异常处理**: 处理录单过程中的各种异常

### 7.3 特殊处理逻辑

```python
def handle_cbec_mode(self, data):
    """跨境电商模式特殊处理"""
    # 设置更长的超时时间
    self.timeout = 2 * 3600  # 2小时

    # 特殊的页面元素定位
    cbec_elements = {
        'goods_category': '#cbec_goods_category',
        'customs_code': '#customs_code',
        'origin_country': '#origin_country'
    }

    # 填写跨境电商特有字段
    for field, selector in cbec_elements.items():
        if field in data:
            self.fill_field(selector, data[field])
```

## 8. 监控和日志

### 8.1 关键指标

- **模式成功率**: 按模式统计的录单成功率
- **账户性能**: 各账户的处理性能
- **身份验证**: 身份证验证的成功率
- **超时统计**: 各模式的超时情况

### 8.2 日志记录

```php
// 开始处理
Log::info('Pingan processing started', [
    'order_no' => $data['order_no'],
    'mode' => $mode,
    'account' => $account,
    'holder_type' => $holder_info['id_type']
]);

// 身份验证
Log::debug('ID validation', [
    'order_no' => $data['order_no'],
    'id_type' => $id_type,
    'validation_result' => $validation_result
]);

// 处理完成
Log::info('Pingan processing completed', [
    'order_no' => $data['order_no'],
    'success' => $success,
    'policy_no' => $policy_no ?? null,
    'processing_time' => $processing_time,
    'retry_count' => $retry_count
]);
```

## 9. Go 重构建议

### 9.1 适配器结构

```go
type PinganAdapter struct {
    config   *PinganConfig
    client   *http.Client
    logger   *zap.Logger
    accounts map[string]*PinganAccount
}

type PinganAccount struct {
    Description string `yaml:"description"`
    HolderName  string `yaml:"holder_name"`
    HolderID    string `yaml:"holder_id"`
    IDType      string `yaml:"id_type"`
}

type PinganConfig struct {
    Accounts map[string]*PinganAccount `yaml:"accounts"`
    Timeouts map[string]time.Duration  `yaml:"timeouts"`
    Scripts  *ScriptConfig             `yaml:"scripts"`
}
```

### 9.2 身份验证服务

```go
type IDValidator struct {
    logger *zap.Logger
}

func (v *IDValidator) ValidateIDCard(idNumber string) error {
    if len(idNumber) == 18 {
        return v.validate18DigitID(idNumber)
    } else if len(idNumber) == 15 {
        return v.validate15DigitID(idNumber)
    }
    return errors.New("invalid ID card format")
}

func (v *IDValidator) validate18DigitID(id string) error {
    // 18位身份证校验逻辑
    pattern := `^\d{17}[\dXx]$`
    matched, _ := regexp.MatchString(pattern, id)
    if !matched {
        return errors.New("invalid 18-digit ID format")
    }

    // 校验码验证
    return v.validateChecksum(id)
}
```

### 9.3 模式处理器

```go
type ModeProcessor interface {
    Process(ctx context.Context, data *InsuranceData) (*ProcessResult, error)
    GetTimeout() time.Duration
    GetMode() string
}

type CBECProcessor struct {
    timeout time.Duration
    logger  *zap.Logger
}

func (p *CBECProcessor) Process(ctx context.Context, data *InsuranceData) (*ProcessResult, error) {
    // 跨境电商特殊处理逻辑
    ctx, cancel := context.WithTimeout(ctx, p.timeout)
    defer cancel()

    // 处理逻辑
    return p.processWithSpecialHandling(ctx, data)
}

func (p *CBECProcessor) GetTimeout() time.Duration {
    return 2 * time.Hour // 2小时超时
}
```

这份文档详细说明了平安财险适配器的所有业务逻辑，包括多模式处理、身份验证、账户配置等核心功能。
