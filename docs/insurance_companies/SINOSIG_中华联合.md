# 中华联合 (SINOSIG) 适配器详细说明

## 1. 基本信息

- **保险公司**: 中华联合财产保险股份有限公司
- **适配器类型**: API接口 (API)
- **支持模式**: API_SINOSIG, API_SINOSIG_QZ
- **控制器**: SinosigController.php
- **数据格式**: XML
- **特殊处理**: 需要对%符号进行HTML编码

## 2. 业务流程

### 2.1 API调用流程图

```mermaid
graph TD
    A[接收保单数据] --> B[产品配置识别]
    B --> C{判断产品类型}
    C -->|保呀产品| D[BAOYA配置]
    C -->|其他产品| E[默认配置]
    D --> F[包装类型映射]
    E --> F
    F --> G[特殊字符处理]
    G --> H[XML数据构建]
    H --> I[API请求发送]
    I --> J{响应状态}
    J -->|成功| K[解析XML响应]
    J -->|失败| L[错误处理]
    K --> M[提取保单信息]
    M --> N[返回结果]
    L --> O[重试机制]
    O --> I
```

### 2.2 模式差异

| 模式 | 说明 | 特殊处理 |
|------|------|----------|
| API_SINOSIG | 标准中华联合接口 | 标准处理流程 |
| API_SINOSIG_QZ | 钦州中华联合接口 | 可能有地区特殊配置 |

## 3. 产品配置详解

### 3.1 保呀产品配置

```json
{
  "name": "保呀产品",
  "sys_flag": "BAOYA",
  "com_code": "07710200",
  "protocol_no": "10771YAB02023000006",
  "operate_code": "BAOYA",
  "underwrite_status": "00"
}
```

**配置说明**:
- `sys_flag`: 系统标识，用于区分不同的业务系统
- `com_code`: 公司代码，标识承保机构
- `protocol_no`: 协议号，业务协议编号
- `operate_code`: 操作代码，用于API调用识别
- `underwrite_status`: 承保状态，00表示正常承保

### 3.2 默认产品配置

```json
{
  "name": "阿拉丁产品",
  "sys_flag": "aladdin",
  "com_code": "07514300",
  "protocol_no": "10771YAB02020000035",
  "operate_code": "aladdin",
  "underwrite_status": "01"
}
```

**配置说明**:
- 与保呀产品类似，但使用不同的公司代码和协议号
- `underwrite_status`: 01表示需要人工审核

## 4. 数据映射规则

### 4.1 包装类型映射

| 平台包装类型 | 中华联合代码 | 中华联合描述 |
|-------------|-------------|-------------|
| 裸装 | 024 | 标准包装 |
| 散装 | 024 | 标准包装 |
| 纸箱 | 002 | 纸箱 |
| 木箱 | 001 | 木箱 |
| 袋装 | 023 | 袋子 |
| 托盘 | 020 | 托盘 |
| 桶装 | 019 | 桶 |

### 4.2 产品识别规则

```php
protected function getProductConfig($product_id) {
    // 特殊产品ID识别
    if ($product_id == '2020022194297') {
        return $this->product_settings['2020022194297']; // 保呀产品
    }
    
    // 默认配置
    return $this->product_settings['default']; // 阿拉丁产品
}
```

## 5. 特殊字符处理

### 5.1 HTML编码处理

中华联合系统对特殊字符敏感，特别是%符号需要进行HTML编码：

```php
protected function encodeSpecialCharacters($data) {
    $encoded_data = [];
    
    foreach ($data as $key => $value) {
        if (is_string($value)) {
            // 对%符号进行HTML编码
            $value = str_replace('%', '&#37;', $value);
            // 对其他特殊字符进行编码
            $value = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
        }
        $encoded_data[$key] = $value;
    }
    
    return $encoded_data;
}
```

### 5.2 字符编码规则

```php
protected function processTextFields($text) {
    // 1. 替换%符号
    $text = str_replace('%', '&#37;', $text);
    
    // 2. 处理其他HTML特殊字符
    $text = htmlspecialchars($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    
    // 3. 处理XML特殊字符
    $xml_chars = [
        '&' => '&amp;',
        '<' => '&lt;',
        '>' => '&gt;',
        '"' => '&quot;',
        "'" => '&#39;'
    ];
    
    foreach ($xml_chars as $char => $entity) {
        $text = str_replace($char, $entity, $text);
    }
    
    return $text;
}
```

## 6. XML数据结构

### 6.1 请求XML模板

```xml
<?xml version="1.0" encoding="UTF-8"?>
<request>
    <header>
        <sys_flag>{sys_flag}</sys_flag>
        <com_code>{com_code}</com_code>
        <operate_code>{operate_code}</operate_code>
        <protocol_no>{protocol_no}</protocol_no>
        <timestamp>{timestamp}</timestamp>
        <request_id>{request_id}</request_id>
    </header>
    <body>
        <policy_data>
            <order_no>{order_no}</order_no>
            <holder_name>{holder_name}</holder_name>
            <holder_id>{holder_id}</holder_id>
            <recognizee_name>{recognizee_name}</recognizee_name>
            <goods_name>{goods_name}</goods_name>
            <packer_code>{packer_code}</packer_code>
            <packer_desc>{packer_desc}</packer_desc>
            <insured_amount>{insured_amount}</insured_amount>
            <premium>{premium}</premium>
            <from_location>{from_location}</from_location>
            <to_location>{to_location}</to_location>
            <via_location>{via_location}</via_location>
            <departure_date>{departure_date}</departure_date>
            <underwrite_status>{underwrite_status}</underwrite_status>
        </policy_data>
    </body>
</request>
```

### 6.2 响应XML结构

```xml
<?xml version="1.0" encoding="UTF-8"?>
<response>
    <header>
        <result_code>{result_code}</result_code>
        <result_message>{result_message}</result_message>
        <sys_flag>{sys_flag}</sys_flag>
        <timestamp>{timestamp}</timestamp>
    </header>
    <body>
        <policy_result>
            <success>{success}</success>
            <policy_no>{policy_no}</policy_no>
            <apply_no>{apply_no}</apply_no>
            <underwrite_result>{underwrite_result}</underwrite_result>
            <error_info>
                <error_code>{error_code}</error_code>
                <error_message>{error_message}</error_message>
            </error_info>
        </policy_result>
    </body>
</response>
```

## 7. 业务逻辑实现

### 7.1 数据转换处理

```php
protected function transformData($data, $product_config) {
    $transformed = [];
    
    // 产品配置信息
    $transformed['sys_flag'] = $product_config['sys_flag'];
    $transformed['com_code'] = $product_config['com_code'];
    $transformed['operate_code'] = $product_config['operate_code'];
    $transformed['protocol_no'] = $product_config['protocol_no'];
    $transformed['underwrite_status'] = $product_config['underwrite_status'];
    
    // 基础信息
    $transformed['order_no'] = $data['order_no'];
    $transformed['holder_name'] = $this->processTextFields($data['holder_name']);
    $transformed['holder_id'] = $data['holder_id'];
    $transformed['recognizee_name'] = $this->processTextFields($data['recognizee_name']);
    
    // 货物信息
    $transformed['goods_name'] = $this->processTextFields($data['goods_name']);
    
    // 包装信息映射
    $packer_mapping = $this->getPackerMapping($data['packer_type']);
    $transformed['packer_code'] = $packer_mapping[0];
    $transformed['packer_desc'] = $packer_mapping[1];
    
    // 保险信息
    $transformed['insured_amount'] = $data['insured_amount'];
    $transformed['premium'] = $data['premium'];
    
    // 运输信息
    $transformed['from_location'] = $this->processTextFields($data['from_loc']);
    $transformed['to_location'] = $this->processTextFields($data['to_loc']);
    $transformed['via_location'] = $this->processTextFields($data['via_loc'] ?? '');
    $transformed['departure_date'] = date('Y-m-d H:i:s', $data['departure_date']);
    
    // 时间戳和请求ID
    $transformed['timestamp'] = date('Y-m-d H:i:s');
    $transformed['request_id'] = $this->generateRequestId();
    
    return $transformed;
}
```

### 7.2 包装类型映射

```php
protected function getPackerMapping($packer_type) {
    $mappings = [
        '裸装' => ['024', '标准包装'],
        '散装' => ['024', '标准包装'],
        '纸箱' => ['002', '纸箱'],
        '木箱' => ['001', '木箱'],
        '袋装' => ['023', '袋子'],
        '托盘' => ['020', '托盘'],
        '桶装' => ['019', '桶']
    ];
    
    if (isset($mappings[$packer_type])) {
        return $mappings[$packer_type];
    }
    
    // 默认标准包装
    return ['024', '标准包装'];
}
```

### 7.3 XML构建

```php
protected function buildXMLRequest($data) {
    $xml = new SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><request></request>');
    
    // 头部信息
    $header = $xml->addChild('header');
    $header->addChild('sys_flag', $data['sys_flag']);
    $header->addChild('com_code', $data['com_code']);
    $header->addChild('operate_code', $data['operate_code']);
    $header->addChild('protocol_no', $data['protocol_no']);
    $header->addChild('timestamp', $data['timestamp']);
    $header->addChild('request_id', $data['request_id']);
    
    // 主体信息
    $body = $xml->addChild('body');
    $policy_data = $body->addChild('policy_data');
    
    // 保单数据
    $policy_data->addChild('order_no', $data['order_no']);
    $policy_data->addChild('holder_name', $data['holder_name']);
    $policy_data->addChild('holder_id', $data['holder_id']);
    $policy_data->addChild('recognizee_name', $data['recognizee_name']);
    $policy_data->addChild('goods_name', $data['goods_name']);
    $policy_data->addChild('packer_code', $data['packer_code']);
    $policy_data->addChild('packer_desc', $data['packer_desc']);
    $policy_data->addChild('insured_amount', $data['insured_amount']);
    $policy_data->addChild('premium', $data['premium']);
    $policy_data->addChild('from_location', $data['from_location']);
    $policy_data->addChild('to_location', $data['to_location']);
    $policy_data->addChild('via_location', $data['via_location']);
    $policy_data->addChild('departure_date', $data['departure_date']);
    $policy_data->addChild('underwrite_status', $data['underwrite_status']);
    
    return $xml->asXML();
}
```

## 8. API接口调用

### 8.1 HTTP请求处理

```php
protected function callSinosigAPI($xml_data, $mode) {
    $url = $this->getAPIEndpoint($mode);
    
    $options = [
        'http' => [
            'method' => 'POST',
            'header' => [
                'Content-Type: application/xml; charset=UTF-8',
                'Content-Length: ' . strlen($xml_data),
                'User-Agent: SinosigClient/1.0'
            ],
            'content' => $xml_data,
            'timeout' => 30
        ],
        'ssl' => [
            'verify_peer' => false,
            'verify_peer_name' => false
        ]
    ];
    
    $context = stream_context_create($options);
    $response = file_get_contents($url, false, $context);
    
    if ($response === false) {
        throw new Exception('Sinosig API request failed');
    }
    
    return $this->parseXMLResponse($response);
}
```

### 8.2 响应解析

```php
protected function parseXMLResponse($xml_response) {
    // 处理响应中的HTML编码字符
    $xml_response = html_entity_decode($xml_response, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    
    $xml = simplexml_load_string($xml_response);
    
    if ($xml === false) {
        throw new Exception('Invalid XML response from Sinosig');
    }
    
    $result = [
        'success' => false,
        'policy_no' => null,
        'apply_no' => null,
        'underwrite_result' => null,
        'error_code' => null,
        'error_message' => null
    ];
    
    // 解析头部
    $header = $xml->header;
    $result_code = (string)$header->result_code;
    
    // 解析主体
    if (isset($xml->body->policy_result)) {
        $policy_result = $xml->body->policy_result;
        
        $result['success'] = ((string)$policy_result->success === 'true');
        $result['policy_no'] = (string)$policy_result->policy_no;
        $result['apply_no'] = (string)$policy_result->apply_no;
        $result['underwrite_result'] = (string)$policy_result->underwrite_result;
        
        if (isset($policy_result->error_info)) {
            $error_info = $policy_result->error_info;
            $result['error_code'] = (string)$error_info->error_code;
            $result['error_message'] = (string)$error_info->error_message;
        }
    }
    
    return $result;
}
```

## 9. 错误处理

### 9.1 错误类型

```php
class SinosigErrorHandler {
    const ERROR_CODES = [
        'S001' => '产品配置错误',
        'S002' => '包装类型映射失败',
        'S003' => '特殊字符处理失败',
        'S004' => 'XML构建失败',
        'S005' => 'API调用失败',
        'S006' => 'XML解析失败',
        'S007' => '承保状态异常',
        'S008' => '网络连接超时'
    ];
    
    public function handleSinosigError($error_code, $error_message) {
        switch ($error_code) {
            case 'UNDERWRITE_PENDING':
                return $this->createPendingResult('S007', '需要人工审核');
            case 'NETWORK_ERROR':
                return $this->createRetryableError('S008', '网络连接失败');
            default:
                return $this->createFinalError($error_code, $error_message);
        }
    }
}
```

### 9.2 承保状态处理

```php
protected function handleUnderwriteResult($underwrite_result) {
    switch ($underwrite_result) {
        case '00':
            return ['status' => 'approved', 'message' => '自动承保通过'];
        case '01':
            return ['status' => 'pending', 'message' => '需要人工审核'];
        case '02':
            return ['status' => 'rejected', 'message' => '承保拒绝'];
        default:
            return ['status' => 'unknown', 'message' => '未知承保状态'];
    }
}
```

## 10. Go重构建议

### 10.1 适配器结构

```go
type SinosigAdapter struct {
    config         *SinosigConfig
    httpClient     *http.Client
    logger         *zap.Logger
    textProcessor  *TextProcessor
}

type SinosigConfig struct {
    APIEndpoints     map[string]string `yaml:"api_endpoints"`
    ProductSettings  map[string]*ProductConfig `yaml:"product_settings"`
    PackerMappings   map[string][]string `yaml:"packer_mappings"`
    Timeout          time.Duration `yaml:"timeout"`
}

type ProductConfig struct {
    Name             string `yaml:"name"`
    SysFlag          string `yaml:"sys_flag"`
    ComCode          string `yaml:"com_code"`
    ProtocolNo       string `yaml:"protocol_no"`
    OperateCode      string `yaml:"operate_code"`
    UnderwriteStatus string `yaml:"underwrite_status"`
}
```

### 10.2 文本处理器

```go
type TextProcessor struct {
    logger *zap.Logger
}

func (tp *TextProcessor) ProcessSpecialCharacters(text string) string {
    // 处理%符号
    text = strings.ReplaceAll(text, "%", "&#37;")
    
    // 处理HTML特殊字符
    text = html.EscapeString(text)
    
    return text
}

func (tp *TextProcessor) EncodeForXML(text string) string {
    // XML特殊字符编码
    replacements := map[string]string{
        "&":  "&amp;",
        "<":  "&lt;",
        ">":  "&gt;",
        "\"": "&quot;",
        "'":  "&#39;",
    }
    
    for char, entity := range replacements {
        text = strings.ReplaceAll(text, char, entity)
    }
    
    return text
}
```

### 10.3 XML处理

```go
type SinosigRequest struct {
    XMLName xml.Name `xml:"request"`
    Header  SinosigHeader `xml:"header"`
    Body    SinosigBody   `xml:"body"`
}

type SinosigHeader struct {
    SysFlag     string `xml:"sys_flag"`
    ComCode     string `xml:"com_code"`
    OperateCode string `xml:"operate_code"`
    ProtocolNo  string `xml:"protocol_no"`
    Timestamp   string `xml:"timestamp"`
    RequestID   string `xml:"request_id"`
}

type SinosigBody struct {
    PolicyData PolicyData `xml:"policy_data"`
}

func (s *SinosigAdapter) buildXMLRequest(data *InsuranceData, config *ProductConfig) (*SinosigRequest, error) {
    request := &SinosigRequest{
        Header: SinosigHeader{
            SysFlag:     config.SysFlag,
            ComCode:     config.ComCode,
            OperateCode: config.OperateCode,
            ProtocolNo:  config.ProtocolNo,
            Timestamp:   time.Now().Format("2006-01-02 15:04:05"),
            RequestID:   s.generateRequestID(),
        },
        Body: SinosigBody{
            PolicyData: s.transformToPolicyData(data),
        },
    }
    
    return request, nil
}
```

这份文档详细说明了中华联合API接口的所有技术细节，特别是特殊字符处理和产品配置管理。
