# 史带财险 (STARR) 适配器详细说明

## 1. 基本信息

- **保险公司**: 史带财产保险股份有限公司
- **适配器类型**: API接口 (API)
- **支持模式**: API_STARR
- **控制器**: STARRController.php
- **数据格式**: JSON
- **特殊处理**: 需要生成GUID和MD5签名

## 2. 业务流程

### 2.1 API调用流程图

```mermaid
graph TD
    A[接收保单数据] --> B[身份类型识别]
    B --> C{企业还是个人}
    C -->|企业| D[企业证件处理]
    C -->|个人| E[个人证件处理]
    D --> F[固定配置加载]
    E --> F
    F --> G[GUID生成]
    G --> H[数据签名准备]
    H --> I[MD5签名生成]
    I --> J[JSON数据构建]
    J --> K[API请求发送]
    K --> L{响应状态}
    L -->|成功| M[解析JSON响应]
    L -->|失败| N[错误处理]
    M --> O[提取保单信息]
    O --> P[返回结果]
    N --> Q[重试机制]
    Q --> K
```

### 2.2 数据处理特点

1. **JSON格式**: 使用JSON而非XML进行数据交换
2. **GUID生成**: 每个请求需要生成唯一的GUID
3. **MD5签名**: 对关键数据进行MD5签名验证
4. **身份识别**: 区分企业和个人的证件类型处理

## 3. 固定配置

### 3.1 系统固定配置

```json
{
  "partner_code": "YOUFU",
  "partner_key": "km2a0f13c9dkb8",
  "product_code": 60021,
  "operation_type": "006",
  "pay_treatment_method": "2",
  "duration_type": "M",
  "duration": 1,
  "cargo_transport_way": "3",
  "email": "<EMAIL>"
}
```

**配置说明**:
- `partner_code`: 合作伙伴代码，标识接入方
- `partner_key`: 合作伙伴密钥，用于签名验证
- `product_code`: 产品代码，史带系统中的产品标识
- `operation_type`: 操作类型，006表示投保操作
- `pay_treatment_method`: 支付处理方式
- `duration_type`: 保险期间类型，M表示月
- `duration`: 保险期间，1表示1个月
- `cargo_transport_way`: 货物运输方式
- `email`: 通知邮箱地址

### 3.2 身份处理配置

```json
{
  "company_part_type": "QY",
  "company_card_type": "104",
  "individual_part_type": "GR",
  "individual_card_type": "1"
}
```

**配置说明**:
- `company_part_type`: 企业参与方类型
- `company_card_type`: 企业证件类型（营业执照）
- `individual_part_type`: 个人参与方类型
- `individual_card_type`: 个人证件类型（身份证）

## 4. 业务逻辑实现

### 4.1 身份类型识别

```php
protected function identifyPartyType($holder_id) {
    // 判断是否为营业执照号（18位数字字母组合）
    if (preg_match('/^[0-9A-Z]{18}$/', $holder_id)) {
        return [
            'part_type' => $this->config['company_part_type'],
            'card_type' => $this->config['company_card_type']
        ];
    }
    
    // 判断是否为身份证号（15位或18位）
    if (preg_match('/^\d{15}$/', $holder_id) || preg_match('/^\d{17}[\dXx]$/', $holder_id)) {
        return [
            'part_type' => $this->config['individual_part_type'],
            'card_type' => $this->config['individual_card_type']
        ];
    }
    
    // 默认按个人处理
    return [
        'part_type' => $this->config['individual_part_type'],
        'card_type' => $this->config['individual_card_type']
    ];
}
```

### 4.2 GUID生成

```php
protected function generateGUID() {
    // 生成标准GUID格式
    $data = random_bytes(16);
    $data[6] = chr(ord($data[6]) & 0x0f | 0x40); // 设置版本号为4
    $data[8] = chr(ord($data[8]) & 0x3f | 0x80); // 设置变体位
    
    return sprintf(
        '%08s-%04s-%04s-%04s-%12s',
        bin2hex(substr($data, 0, 4)),
        bin2hex(substr($data, 4, 2)),
        bin2hex(substr($data, 6, 2)),
        bin2hex(substr($data, 8, 2)),
        bin2hex(substr($data, 10, 6))
    );
}
```

### 4.3 MD5签名生成

```php
protected function generateSignature($data) {
    // 构建签名字符串
    $sign_string = '';
    $sign_string .= $data['partner_code'];
    $sign_string .= $data['order_no'];
    $sign_string .= $data['insured_amount'];
    $sign_string .= $data['premium'];
    $sign_string .= $data['holder_name'];
    $sign_string .= $data['holder_id'];
    $sign_string .= $this->config['partner_key']; // 密钥放在最后
    
    // 生成MD5签名
    return md5($sign_string);
}
```

### 4.4 数据转换处理

```php
protected function transformData($data) {
    $transformed = [];
    
    // 固定配置
    $transformed['partner_code'] = $this->config['partner_code'];
    $transformed['product_code'] = $this->config['product_code'];
    $transformed['operation_type'] = $this->config['operation_type'];
    $transformed['pay_treatment_method'] = $this->config['pay_treatment_method'];
    $transformed['duration_type'] = $this->config['duration_type'];
    $transformed['duration'] = $this->config['duration'];
    $transformed['cargo_transport_way'] = $this->config['cargo_transport_way'];
    $transformed['email'] = $this->config['email'];
    
    // 生成GUID
    $transformed['request_id'] = $this->generateGUID();
    
    // 基础信息
    $transformed['order_no'] = $data['order_no'];
    $transformed['holder_name'] = $data['holder_name'];
    $transformed['holder_id'] = $data['holder_id'];
    $transformed['recognizee_name'] = $data['recognizee_name'];
    
    // 身份类型识别
    $party_info = $this->identifyPartyType($data['holder_id']);
    $transformed['part_type'] = $party_info['part_type'];
    $transformed['card_type'] = $party_info['card_type'];
    
    // 货物信息
    $transformed['goods_name'] = $data['goods_name'];
    $transformed['insured_amount'] = $data['insured_amount'];
    $transformed['premium'] = $data['premium'];
    
    // 运输信息
    $transformed['from_location'] = $data['from_loc'];
    $transformed['to_location'] = $data['to_loc'];
    $transformed['via_location'] = $data['via_loc'] ?? '';
    $transformed['departure_date'] = date('Y-m-d H:i:s', $data['departure_date']);
    
    // 时间戳
    $transformed['timestamp'] = date('Y-m-d H:i:s');
    
    // 生成签名
    $transformed['signature'] = $this->generateSignature($transformed);
    
    return $transformed;
}
```

## 5. JSON数据结构

### 5.1 请求JSON结构

```json
{
  "header": {
    "partner_code": "YOUFU",
    "request_id": "550e8400-e29b-41d4-a716-446655440000",
    "timestamp": "2024-01-01 12:00:00",
    "signature": "d41d8cd98f00b204e9800998ecf8427e"
  },
  "body": {
    "policy_info": {
      "order_no": "ORDER123456789",
      "product_code": 60021,
      "operation_type": "006",
      "holder_info": {
        "holder_name": "张三",
        "holder_id": "110101199001011234",
        "part_type": "GR",
        "card_type": "1"
      },
      "recognizee_info": {
        "recognizee_name": "李四"
      },
      "goods_info": {
        "goods_name": "电子产品",
        "insured_amount": 100000.00,
        "premium": 150.00
      },
      "transport_info": {
        "from_location": "北京",
        "to_location": "上海",
        "via_location": "天津",
        "departure_date": "2024-01-01 12:00:00",
        "cargo_transport_way": "3"
      },
      "insurance_info": {
        "duration_type": "M",
        "duration": 1,
        "pay_treatment_method": "2"
      },
      "contact_info": {
        "email": "<EMAIL>"
      }
    }
  }
}
```

### 5.2 响应JSON结构

```json
{
  "header": {
    "result_code": "0000",
    "result_message": "成功",
    "timestamp": "2024-01-01 12:00:01",
    "response_id": "550e8400-e29b-41d4-a716-446655440001"
  },
  "body": {
    "policy_result": {
      "success": true,
      "policy_no": "STARR202401010001",
      "apply_no": "APPLY202401010001",
      "underwrite_status": "approved",
      "effective_date": "2024-01-01 12:00:00",
      "expire_date": "2024-02-01 12:00:00",
      "error_info": {
        "error_code": null,
        "error_message": null
      }
    }
  }
}
```

## 6. API接口调用

### 6.1 HTTP请求处理

```php
protected function callStarrAPI($json_data) {
    $url = $this->getAPIEndpoint();
    
    $options = [
        'http' => [
            'method' => 'POST',
            'header' => [
                'Content-Type: application/json; charset=UTF-8',
                'Content-Length: ' . strlen($json_data),
                'User-Agent: StarrClient/1.0',
                'Accept: application/json'
            ],
            'content' => $json_data,
            'timeout' => 30
        ],
        'ssl' => [
            'verify_peer' => true,
            'verify_peer_name' => true,
            'cafile' => $this->getCertificatePath()
        ]
    ];
    
    $context = stream_context_create($options);
    $response = file_get_contents($url, false, $context);
    
    if ($response === false) {
        throw new Exception('Starr API request failed');
    }
    
    return $this->parseJSONResponse($response);
}
```

### 6.2 JSON构建

```php
protected function buildJSONRequest($data) {
    $request = [
        'header' => [
            'partner_code' => $data['partner_code'],
            'request_id' => $data['request_id'],
            'timestamp' => $data['timestamp'],
            'signature' => $data['signature']
        ],
        'body' => [
            'policy_info' => [
                'order_no' => $data['order_no'],
                'product_code' => $data['product_code'],
                'operation_type' => $data['operation_type'],
                'holder_info' => [
                    'holder_name' => $data['holder_name'],
                    'holder_id' => $data['holder_id'],
                    'part_type' => $data['part_type'],
                    'card_type' => $data['card_type']
                ],
                'recognizee_info' => [
                    'recognizee_name' => $data['recognizee_name']
                ],
                'goods_info' => [
                    'goods_name' => $data['goods_name'],
                    'insured_amount' => floatval($data['insured_amount']),
                    'premium' => floatval($data['premium'])
                ],
                'transport_info' => [
                    'from_location' => $data['from_location'],
                    'to_location' => $data['to_location'],
                    'via_location' => $data['via_location'],
                    'departure_date' => $data['departure_date'],
                    'cargo_transport_way' => $data['cargo_transport_way']
                ],
                'insurance_info' => [
                    'duration_type' => $data['duration_type'],
                    'duration' => intval($data['duration']),
                    'pay_treatment_method' => $data['pay_treatment_method']
                ],
                'contact_info' => [
                    'email' => $data['email']
                ]
            ]
        ]
    ];
    
    return json_encode($request, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
}
```

### 6.3 响应解析

```php
protected function parseJSONResponse($json_response) {
    $response = json_decode($json_response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON response from Starr: ' . json_last_error_msg());
    }
    
    $result = [
        'success' => false,
        'policy_no' => null,
        'apply_no' => null,
        'underwrite_status' => null,
        'effective_date' => null,
        'expire_date' => null,
        'error_code' => null,
        'error_message' => null
    ];
    
    // 解析头部
    if (isset($response['header'])) {
        $header = $response['header'];
        $result_code = $header['result_code'] ?? '';
        
        if ($result_code === '0000') {
            $result['success'] = true;
        }
    }
    
    // 解析主体
    if (isset($response['body']['policy_result'])) {
        $policy_result = $response['body']['policy_result'];
        
        $result['policy_no'] = $policy_result['policy_no'] ?? null;
        $result['apply_no'] = $policy_result['apply_no'] ?? null;
        $result['underwrite_status'] = $policy_result['underwrite_status'] ?? null;
        $result['effective_date'] = $policy_result['effective_date'] ?? null;
        $result['expire_date'] = $policy_result['expire_date'] ?? null;
        
        if (isset($policy_result['error_info'])) {
            $error_info = $policy_result['error_info'];
            $result['error_code'] = $error_info['error_code'];
            $result['error_message'] = $error_info['error_message'];
        }
    }
    
    return $result;
}
```

## 7. 错误处理

### 7.1 错误类型

```php
class StarrErrorHandler {
    const ERROR_CODES = [
        'ST001' => 'GUID生成失败',
        'ST002' => 'MD5签名生成失败',
        'ST003' => '身份类型识别失败',
        'ST004' => 'JSON构建失败',
        'ST005' => 'API调用失败',
        'ST006' => 'JSON解析失败',
        'ST007' => '签名验证失败',
        'ST008' => '网络连接超时'
    ];
    
    public function handleStarrError($error_code, $error_message) {
        switch ($error_code) {
            case 'SIGNATURE_ERROR':
                return $this->createFinalError('ST007', '签名验证失败');
            case 'NETWORK_ERROR':
                return $this->createRetryableError('ST008', '网络连接失败');
            case 'SYSTEM_ERROR':
                return $this->createRetryableError('ST005', '史带系统错误: ' . $error_message);
            default:
                return $this->createFinalError($error_code, $error_message);
        }
    }
}
```

### 7.2 签名验证

```php
protected function verifySignature($response_data, $expected_signature) {
    // 构建验证字符串
    $verify_string = '';
    $verify_string .= $response_data['partner_code'];
    $verify_string .= $response_data['response_id'];
    $verify_string .= $response_data['result_code'];
    $verify_string .= $this->config['partner_key'];
    
    $calculated_signature = md5($verify_string);
    
    return $calculated_signature === $expected_signature;
}
```

## 8. Go重构建议

### 8.1 适配器结构

```go
type StarrAdapter struct {
    config     *StarrConfig
    httpClient *http.Client
    logger     *zap.Logger
    signer     *SignatureGenerator
}

type StarrConfig struct {
    APIEndpoint         string `yaml:"api_endpoint"`
    PartnerCode         string `yaml:"partner_code"`
    PartnerKey          string `yaml:"partner_key"`
    ProductCode         int    `yaml:"product_code"`
    OperationType       string `yaml:"operation_type"`
    PayTreatmentMethod  string `yaml:"pay_treatment_method"`
    DurationType        string `yaml:"duration_type"`
    Duration            int    `yaml:"duration"`
    CargoTransportWay   string `yaml:"cargo_transport_way"`
    Email               string `yaml:"email"`
    CompanyPartType     string `yaml:"company_part_type"`
    CompanyCardType     string `yaml:"company_card_type"`
    IndividualPartType  string `yaml:"individual_part_type"`
    IndividualCardType  string `yaml:"individual_card_type"`
}
```

### 8.2 GUID生成器

```go
type GUIDGenerator struct{}

func (g *GUIDGenerator) Generate() string {
    u := uuid.New()
    return u.String()
}
```

### 8.3 签名生成器

```go
type SignatureGenerator struct {
    partnerKey string
}

func NewSignatureGenerator(partnerKey string) *SignatureGenerator {
    return &SignatureGenerator{
        partnerKey: partnerKey,
    }
}

func (s *SignatureGenerator) GenerateSignature(data *StarrRequestData) string {
    signString := fmt.Sprintf("%s%s%s%s%s%s%s",
        data.PartnerCode,
        data.OrderNo,
        data.InsuredAmount,
        data.Premium,
        data.HolderName,
        data.HolderID,
        s.partnerKey,
    )
    
    hash := md5.Sum([]byte(signString))
    return hex.EncodeToString(hash[:])
}
```

### 8.4 身份识别器

```go
type IdentityRecognizer struct {
    config *StarrConfig
}

func (ir *IdentityRecognizer) IdentifyPartyType(holderID string) (string, string) {
    // 营业执照号判断
    if matched, _ := regexp.MatchString(`^[0-9A-Z]{18}$`, holderID); matched {
        return ir.config.CompanyPartType, ir.config.CompanyCardType
    }
    
    // 身份证号判断
    if matched, _ := regexp.MatchString(`^\d{15}$|^\d{17}[\dXx]$`, holderID); matched {
        return ir.config.IndividualPartType, ir.config.IndividualCardType
    }
    
    // 默认个人
    return ir.config.IndividualPartType, ir.config.IndividualCardType
}
```

这份文档详细说明了史带财险API接口的所有技术细节，包括GUID生成、MD5签名、身份识别等核心功能。
