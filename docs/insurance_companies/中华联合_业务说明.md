# 中华联合 (SINOSIG) 业务逻辑说明

## 1. 基本信息

**保险公司**: 中华联合财产保险股份有限公司  
**适配器类型**: API接口 (API)  
**支持模式**: API_SINOSIG, API_SINOSIG_QZ  
**数据格式**: XML  
**特殊处理**: 需要对%符号进行HTML编码  

## 2. 业务流程概述

### 2.1 API调用流程

中华联合采用XML格式的API接口，具有以下特点：

1. **产品配置识别**: 根据产品ID识别使用保呀产品还是默认产品配置
2. **包装类型映射**: 将平台的包装类型映射为中华联合的标准代码
3. **特殊字符处理**: 对%符号等特殊字符进行HTML编码处理
4. **XML数据构建**: 构建符合中华联合API规范的XML请求
5. **API请求发送**: 发送HTTP POST请求到中华联合接口
6. **响应解析**: 解析XML响应并提取保单信息

### 2.2 模式差异

**API_SINOSIG**: 标准中华联合接口，使用标准处理流程  
**API_SINOSIG_QZ**: 钦州中华联合接口，可能有地区特殊配置  

两种模式在基本处理逻辑上相同，主要差异在于API端点和可能的地区特殊配置。

## 3. 产品配置体系

### 3.1 保呀产品配置

当产品ID为"2020022194297"时，系统使用保呀产品配置：

**系统标识**: BAOYA  
**公司代码**: 07710200  
**协议号**: 10771YAB02023000006  
**操作代码**: BAOYA  
**承保状态**: 00 (正常承保)  

### 3.2 默认产品配置

对于其他产品ID，系统使用阿拉丁产品配置：

**系统标识**: aladdin  
**公司代码**: 07514300  
**协议号**: 10771YAB02020000035  
**操作代码**: aladdin  
**承保状态**: 01 (需要人工审核)  

### 3.3 配置选择逻辑

系统根据传入的产品ID自动选择对应的配置：
- 特殊产品ID "2020022194297" 使用保呀配置
- 其他所有产品ID使用默认阿拉丁配置
- 配置信息包括系统标识、公司代码、协议号等关键参数

## 4. 数据映射规则

### 4.1 包装类型映射

中华联合系统要求将包装类型映射为特定代码：

**裸装/散装**: 代码024, 描述"标准包装"  
**纸箱**: 代码002, 描述"纸箱"  
**木箱**: 代码001, 描述"木箱"  
**袋装**: 代码023, 描述"袋子"  
**托盘**: 代码020, 描述"托盘"  
**桶装**: 代码019, 描述"桶"  

如果传入的包装类型不在映射表中，系统默认使用"标准包装"(代码024)。

### 4.2 产品识别规则

系统通过产品ID进行产品配置的识别：
- 检查产品ID是否为特殊值"2020022194297"
- 如果是，则使用保呀产品的配置参数
- 如果不是，则使用默认的阿拉丁产品配置
- 这种设计允许系统灵活支持不同的产品线

## 5. 特殊字符处理

### 5.1 HTML编码需求

中华联合系统对特殊字符敏感，特别是%符号需要特殊处理：

**%符号处理**: 将%替换为&#37;  
**HTML特殊字符**: 对<、>、&、"、'等进行HTML实体编码  
**XML特殊字符**: 确保XML格式的正确性  

### 5.2 字符编码流程

对所有文本字段进行以下处理：
1. 首先替换%符号为HTML实体
2. 然后处理其他HTML特殊字符
3. 最后处理XML特殊字符
4. 确保编码后的文本符合XML规范

### 5.3 编码规则

**%符号**: % → &#37;  
**小于号**: < → &lt;  
**大于号**: > → &gt;  
**和号**: & → &amp;  
**双引号**: " → &quot;  
**单引号**: ' → &#39;  

## 6. XML数据结构

### 6.1 请求XML结构

中华联合API要求的XML请求包含：

**头部信息**: 系统标识、公司代码、操作代码、协议号、时间戳、请求ID  
**保单数据**: 订单号、投保人信息、被保险人信息、货物信息、包装信息、保险金额、保费、运输信息、承保状态  

### 6.2 响应XML结构

中华联合系统返回的XML响应包含：

**头部信息**: 结果代码、结果消息、系统标识、时间戳  
**保单结果**: 成功标志、保单号、申请号、承保结果、错误信息  

## 7. 业务逻辑实现

### 7.1 数据转换处理

系统对传入数据进行以下转换：

1. **产品配置加载**: 根据产品ID选择对应的配置信息
2. **基础信息处理**: 订单号、投保人信息、被保险人信息等
3. **货物信息处理**: 货物名称的特殊字符编码处理
4. **包装信息映射**: 将包装类型映射为中华联合代码
5. **运输信息处理**: 起止地点的特殊字符编码
6. **时间信息处理**: 格式化出发时间
7. **元数据添加**: 添加时间戳和请求ID

### 7.2 XML构建过程

使用SimpleXMLElement构建XML请求：

1. **创建根元素**: 创建request根元素
2. **添加头部**: 系统标识、公司代码等配置信息
3. **构建主体**: 添加policy_data保单数据
4. **填充字段**: 按照中华联合API规范填充各字段
5. **特殊处理**: 对文本内容进行特殊字符编码
6. **生成XML**: 输出符合规范的XML字符串

### 7.3 API调用处理

HTTP请求处理包括：

1. **请求配置**: 设置Content-Type为application/xml
2. **SSL配置**: 配置SSL验证参数
3. **超时设置**: 30秒的请求超时
4. **错误处理**: 捕获网络和HTTP错误
5. **响应处理**: 先进行HTML解码再解析XML

## 8. 错误处理机制

### 8.1 错误类型分类

**产品配置错误**: 产品ID无效或配置缺失  
**包装类型映射失败**: 无法识别的包装类型  
**特殊字符处理失败**: 字符编码过程出错  
**XML构建失败**: XML格式错误  
**API调用失败**: 网络连接或HTTP错误  
**XML解析失败**: 响应格式错误  
**承保状态异常**: 需要人工审核或承保拒绝  

### 8.2 承保状态处理

中华联合系统返回不同的承保状态：

**00 (自动承保通过)**: 保单立即生效  
**01 (需要人工审核)**: 需要等待人工审核  
**02 (承保拒绝)**: 承保申请被拒绝  
**其他状态**: 未知状态，需要进一步确认  

### 8.3 重试策略

对于可重试的错误：
- **网络错误**: 自动重试，最多3次
- **系统维护**: 延后重试
- **临时错误**: 使用指数退避算法

对于不可重试的错误：
- **数据验证错误**: 直接返回失败
- **承保拒绝**: 不进行重试
- **配置错误**: 检查配置后返回错误

## 9. 监控和日志

### 9.1 关键指标监控

**API成功率**: 按产品配置统计的API调用成功率  
**承保通过率**: 自动承保通过的比例  
**字符编码准确性**: 特殊字符处理的准确性  
**响应时间**: API调用的平均响应时间  

### 9.2 日志记录

系统记录以下关键信息：
- **产品配置**: 使用的产品配置类型（保呀或阿拉丁）
- **字符处理**: 特殊字符编码的处理结果
- **包装映射**: 包装类型的映射结果
- **API调用**: 请求参数、响应时间、结果状态
- **承保结果**: 承保状态和相关信息

## 10. Go重构建议

### 10.1 架构设计

**产品配置管理器**: 管理不同产品的配置信息  
**字符编码处理器**: 专门处理特殊字符编码  
**包装类型映射器**: 管理包装类型映射规则  
**XML处理器**: 处理XML构建和解析  
**承保状态处理器**: 处理不同的承保状态  

### 10.2 核心组件

**配置管理**: 使用YAML文件管理产品配置和映射表  
**文本处理**: 实现可配置的字符编码规则  
**XML模板**: 使用Go结构体定义XML格式  
**HTTP客户端**: 支持SSL和超时配置的HTTP客户端  
**状态机**: 处理承保状态的转换逻辑  

### 10.3 技术选型

**配置管理**: Viper管理产品配置和映射规则  
**XML处理**: encoding/xml包处理XML序列化  
**字符编码**: html包处理HTML实体编码  
**HTTP客户端**: net/http包配置SSL和超时  
**日志记录**: Zap记录结构化日志  

这种设计能够有效处理中华联合系统的特殊要求，同时保持代码的清晰性和可维护性。
