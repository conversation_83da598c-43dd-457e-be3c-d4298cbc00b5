# 中意雇主 (GROUP_ZHONGYI) 业务逻辑说明

## 1. 基本信息

**保险公司**: 中意人寿保险有限公司  
**适配器类型**: API接口 (API)  
**支持模式**: API_GROUP_ZY  
**数据格式**: JSON  
**业务类型**: 雇主责任险  

## 2. 业务流程概述

### 2.1 雇主责任险特点

中意雇主责任险是专门针对雇主责任的保险产品，具有以下特点：

**雇主保障**: 为雇主提供雇员工伤事故的责任保障  
**雇员管理**: 需要管理详细的雇员信息和保障范围  
**动态保费**: 根据雇员数量、风险等级、行业类型动态计算保费  
**批量操作**: 支持批量添加和管理雇员信息  
**风险评估**: 基于行业、职位、年龄等因素进行风险评估  

### 2.2 API调用流程

中意雇主责任险的处理流程包括：

1. **雇主信息验证**: 验证雇主企业的基本信息和资质
2. **雇员信息处理**: 处理和验证雇员的详细信息
3. **风险等级评估**: 根据行业和职位评估风险等级
4. **保费计算**: 基于多种因素动态计算保险费
5. **JSON数据构建**: 构建符合中意API规范的JSON请求
6. **API请求发送**: 发送HTTPS POST请求到中意接口
7. **响应解析**: 解析JSON响应并提取保单信息
8. **承保结果处理**: 处理核保结果和特殊条件

## 3. 业务配置管理

### 3.1 基础配置

中意雇主责任险的基础配置包括：

**产品代码**: GROUP_ZY_001  
**API版本**: 1.0  
**保障期间**: 1年  
**最少雇员**: 1人  
**最多雇员**: 1000人  
**默认保障金额**: 50万元  
**默认币种**: 人民币  

### 3.2 风险等级配置

系统根据行业类型划分风险等级：

**低风险行业**:
- 行业类型: 办公室工作、咨询服务、软件开发
- 基础费率: 0.2%
- 保障倍数: 1.0倍

**中等风险行业**:
- 行业类型: 制造业、零售业、餐饮业
- 基础费率: 0.5%
- 保障倍数: 1.2倍

**高风险行业**:
- 行业类型: 建筑业、化工业、采矿业
- 基础费率: 1.0%
- 保障倍数: 1.5倍

### 3.3 配置选择逻辑

系统根据雇主的行业信息自动选择对应的风险等级配置，如果无法匹配则默认使用中等风险配置。

## 4. 雇主信息处理

### 4.1 企业基本信息

系统处理雇主企业的以下信息：

**企业名称**: 完整的企业法人名称  
**统一社会信用代码**: 18位企业标识码  
**法定代表人**: 企业法人代表姓名  
**联系人信息**: 联系人姓名、电话、邮箱  
**企业地址**: 企业注册地址和经营地址  
**成立时间**: 企业注册成立时间  

### 4.2 经营信息

**行业类型**: 企业所属的行业分类  
**经营范围**: 企业的主要经营业务  
**企业规模**: 根据雇员数量确定企业规模  
**风险评估**: 基于行业类型进行风险等级评估  

### 4.3 信息验证

系统对雇主信息进行以下验证：
- 企业名称和统一社会信用代码的一致性
- 联系人信息的完整性和有效性
- 行业类型的准确性和风险等级匹配
- 经营范围与实际业务的符合性

## 5. 雇员信息管理

### 5.1 雇员基本信息

系统管理每个雇员的详细信息：

**个人信息**: 姓名、身份证号、性别、出生日期  
**工作信息**: 入职日期、职位、部门、工作地点  
**薪资信息**: 月薪、年薪等薪资数据  
**风险分类**: 根据职位确定的风险类别  

### 5.2 风险分类

系统根据职位对雇员进行风险分类：

**高风险职位**: 电工、焊工、高空作业、机械操作员、化学品处理员、司机、保安  
**中等风险职位**: 销售员、客服、仓库管理员、清洁工、厨师、服务员、技术员  
**低风险职位**: 办公室工作人员、管理人员、文员等  

### 5.3 衍生信息计算

系统自动计算以下衍生信息：
- **年龄**: 根据出生日期计算当前年龄
- **工龄**: 根据入职日期计算工作年限
- **风险系数**: 根据年龄、职位、工龄综合确定

## 6. 保费计算机制

### 6.1 计算因子

保费计算考虑以下因子：

**年龄因子**:
- 25岁以下: 1.1倍（年轻员工风险较高）
- 25-35岁: 1.0倍（标准费率）
- 35-50岁: 0.95倍（经验丰富，风险较低）
- 50-60岁: 1.05倍（年龄较大，风险上升）
- 60岁以上: 1.2倍（高龄员工风险较高）

**职位因子**:
- 低风险职位: 0.8倍
- 中等风险职位: 1.0倍
- 高风险职位: 1.5倍

**工龄因子**:
- 1年以下: 1.2倍（新员工风险较高）
- 1-3年: 1.1倍（经验较少）
- 3-10年: 1.0倍（标准费率）
- 10年以上: 0.9倍（经验丰富，风险较低）

### 6.2 团体折扣

根据雇员总数给予团体折扣：
- 100人以上: 15%折扣
- 50-99人: 10%折扣
- 20-49人: 5%折扣
- 20人以下: 无折扣

### 6.3 计算流程

保费计算的具体流程：
1. 计算每个雇员的基础保费（保障金额 × 基础费率）
2. 应用年龄、职位、工龄因子调整个人保费
3. 汇总所有雇员的保费得到总保费
4. 应用团体折扣得到最终保费
5. 四舍五入到分位

## 7. API接口调用

### 7.1 认证机制

中意API使用OAuth 2.0认证机制：

**客户端认证**: 使用客户端ID和密钥获取访问令牌  
**令牌管理**: 缓存访问令牌并在过期前自动刷新  
**权限范围**: 申请雇主责任险相关的API权限  
**安全传输**: 所有API调用都使用HTTPS加密传输  

### 7.2 请求格式

JSON请求包含以下主要部分：
- **头部信息**: API版本、请求ID、时间戳、签名
- **雇主信息**: 企业基本信息、经营信息、风险等级
- **雇员信息**: 雇员列表及详细信息
- **保险信息**: 保障金额、保险期间、保费计算结果

### 7.3 响应处理

系统处理API响应的以下内容：
- **成功响应**: 提取保单号、生效时间、到期时间等信息
- **核保结果**: 处理自动承保、人工审核、拒保等不同结果
- **错误响应**: 解析错误代码和错误消息
- **特殊条件**: 处理核保过程中的特殊条件和要求

## 8. 错误处理机制

### 8.1 错误类型分类

**雇主信息验证失败**: 企业信息不完整或不正确  
**雇员信息不完整**: 雇员信息缺失或格式错误  
**风险等级评估失败**: 无法确定行业风险等级  
**保费计算错误**: 保费计算过程中的数值错误  
**API认证失败**: OAuth认证过程失败  
**核保规则不通过**: 不符合中意的核保规则  
**系统维护中**: 中意系统正在维护  
**网络连接超时**: 网络连接问题  

### 8.2 核保结果处理

不同的核保结果需要不同的处理方式：

**自动承保通过**: 保单立即生效，返回保单信息  
**需要人工审核**: 进入人工审核流程，需要等待审核结果  
**承保拒绝**: 不符合承保条件，返回拒保原因  
**条件承保**: 需要满足特定条件才能承保  

### 8.3 重试策略

对于不同类型的错误采用不同的重试策略：
- **网络错误**: 自动重试，最多3次
- **系统维护**: 延后重试，等待系统恢复
- **认证失败**: 重新获取访问令牌后重试
- **核保拒绝**: 不重试，直接返回结果

## 9. 监控和日志

### 9.1 关键指标监控

**API成功率**: 中意API调用的成功率统计  
**核保通过率**: 自动核保通过的比例  
**保费计算准确性**: 保费计算的准确性验证  
**雇员信息完整性**: 雇员信息的完整性统计  
**响应时间**: API调用的平均响应时间  

### 9.2 业务监控

**风险分布**: 不同风险等级企业的分布情况  
**行业分析**: 各行业的投保情况和风险状况  
**雇员规模**: 不同规模企业的投保趋势  
**保费趋势**: 保费计算的趋势分析  

### 9.3 日志记录

系统记录详细的业务日志：
- **雇主信息**: 企业基本信息和风险评估结果
- **雇员管理**: 雇员信息处理和风险分类结果
- **保费计算**: 详细的保费计算过程和各种因子
- **API调用**: 请求参数、响应时间、核保结果
- **错误处理**: 错误类型、处理过程、最终结果

## 10. Go重构建议

### 10.1 架构设计

**雇主信息管理器**: 处理企业信息验证和风险评估  
**雇员信息管理器**: 管理雇员信息和风险分类  
**保费计算引擎**: 实现复杂的保费计算逻辑  
**风险评估器**: 基于多种因素进行风险评估  
**OAuth客户端**: 处理中意API的认证和令牌管理  

### 10.2 核心组件

**风险评估引擎**: 可配置的风险评估规则和因子  
**保费计算器**: 支持多种计算因子和折扣规则  
**雇员管理**: 批量处理和验证雇员信息  
**核保状态机**: 处理不同核保状态的转换  
**令牌管理器**: 自动管理OAuth访问令牌  

### 10.3 技术选型

**OAuth客户端**: golang.org/x/oauth2包  
**JSON处理**: encoding/json包处理JSON序列化  
**数值计算**: math包处理保费计算  
**时间处理**: time包处理日期和时间  
**HTTP客户端**: net/http包配置HTTPS  
**配置管理**: Viper管理风险等级和计算规则  
**缓存系统**: Redis缓存令牌和配置数据  

这种设计能够有效支持中意雇主责任险的复杂业务需求，同时提供良好的可扩展性和可维护性。
