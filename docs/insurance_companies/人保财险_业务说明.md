# 人保财险 (PICC) 业务逻辑说明

## 1. 基本信息

**保险公司**: 中国人民财产保险股份有限公司  
**适配器类型**: 自动化录单 (AUTO)  
**支持模式**: AUTO_PICC, AUTO_PICC_INTL  
**数据格式**: 网页表单提交  

## 2. 业务流程概述

### 2.1 录单处理流程

人保财险采用自动化网页操作方式，通过Python脚本在人保录单系统中完成保单录入：

1. **模式判断**: 区分国内业务(AUTO_PICC)和国际业务(AUTO_PICC_INTL)
2. **账户配置**: 根据账户参数加载对应的配置信息
3. **数据标准化**: 对传入数据进行格式化和验证处理
4. **业务规则应用**: 根据不同模式应用相应的业务规则
5. **自动化录单**: Python脚本自动填写人保录单系统表单
6. **结果获取**: 获取录单结果和保单号信息

### 2.2 业务模式差异

**AUTO_PICC (国内货运险)**:
- 处理国内货物运输保险业务
- 使用人民币计价
- 应用国内运输条款和费率
- 标准的处理流程和超时设置

**AUTO_PICC_INTL (国际货运险)**:
- 处理国际货物运输保险业务
- 支持外币计价（美元、欧元等）
- 需要处理汇率转换
- 应用国际贸易条款
- 可能涉及海关编码和特殊运输条件

## 3. 账户配置管理

### 3.1 账户体系

人保财险支持多个账户配置，每个账户都有特定的业务范围和默认设置：

**SHTB账户 (上海人保)**:
- 账户类型: 标准账户
- 业务范围: 国内外业务
- 默认币种: 人民币
- 费率类型: 标准费率
- 条款类型: 综合险

### 3.2 账户配置内容

每个账户配置包含以下信息：
- **账户描述**: 账户的用途和适用范围
- **账户类型**: 标准账户或特殊账户
- **业务范围**: 支持的业务类型（国内、国际或两者）
- **默认设置**: 币种、费率类型、条款类型等默认配置

### 3.3 配置选择逻辑

系统根据传入的账户参数自动选择对应配置：
- 加载账户的基本信息和业务范围
- 获取默认的币种和费率设置
- 应用账户特有的处理规则
- 确保业务类型与账户范围匹配

## 4. 数据处理规则

### 4.1 基础数据处理

系统对传入的保单数据进行标准化处理：

**订单信息**: 订单号、货物名称等基础信息  
**投保人信息**: 投保人姓名、证件号码等  
**被保险人信息**: 被保险人姓名和相关信息  
**货物信息**: 货物名称、保险金额、保险费等  
**运输信息**: 起运地、目的地、经停地、运输方式等  

### 4.2 金额格式化

所有金额字段都需要进行格式化处理：
- 确保金额为有效的数字格式
- 保留两位小数精度
- 处理千分位分隔符（如需要）
- 验证金额的合理性范围

### 4.3 时间调整规则

对出发时间进行智能调整以符合人保系统要求：

**当天出发处理**: 如果是当天出发，自动延后1小时  
**分钟数调整**: 如果当前分钟数超过50，再延后1小时  
**24点处理**: 如果调整后超过24点，改为次日00:00  
**格式转换**: 将时间戳转换为人保系统要求的日期时间格式  

这些调整确保录单时间的合理性和系统兼容性。

## 5. 国际业务特殊规则

### 5.1 币种处理

国际业务支持多种币种：
- **默认币种**: 国际业务默认使用美元(USD)
- **支持币种**: 美元、欧元、英镑、日元等主要货币
- **币种验证**: 确保币种代码的有效性
- **币种转换**: 必要时进行币种转换

### 5.2 汇率处理

当使用非人民币币种时：
- **汇率获取**: 从汇率API获取当前汇率
- **汇率缓存**: 缓存汇率数据以提高性能
- **金额转换**: 计算等值人民币金额
- **汇率记录**: 记录使用的汇率和转换时间

### 5.3 国际条款

国际业务需要应用特殊的保险条款：
- **国际运输条款**: 适用于国际货物运输的特殊条款
- **海关编码**: 处理HS编码等海关相关信息
- **贸易术语**: 支持FOB、CIF等国际贸易术语
- **特殊条件**: 处理国际运输的特殊要求

## 6. 自动化录单实现

### 6.1 Python脚本调用

系统通过以下方式调用Python自动化脚本：

**脚本参数**: 包括模式、URL、登录信息、保单数据等  
**超时设置**: 10分钟的处理超时时间  
**重试配置**: 最多3次重试机会  
**日志记录**: 详细记录脚本执行过程  

### 6.2 自动化流程

Python脚本执行以下自动化操作：

1. **环境准备**: 启动浏览器并设置用户代理和超时
2. **系统登录**: 使用账户信息登录人保录单系统
3. **业务选择**: 根据模式选择国内或国际业务
4. **信息录入**: 按顺序填写投保人、被保险人、货物、运输等信息
5. **条款选择**: 选择适当的保险条款
6. **数据校验**: 系统自动校验录入数据的完整性和正确性
7. **提交处理**: 提交录单申请并等待系统处理
8. **结果获取**: 获取录单结果和保单号
9. **异常处理**: 处理录单过程中的各种异常情况

### 6.3 脚本参数说明

传递给Python脚本的参数包括：
- **模式参数**: AUTO_PICC或AUTO_PICC_INTL
- **系统URL**: 人保录单系统的访问地址
- **登录信息**: 账户的登录凭据
- **保单数据**: 处理后的完整保单信息
- **超时时间**: 脚本执行的最大超时时间
- **重试次数**: 失败时的重试次数配置

## 7. 错误处理机制

### 7.1 错误分类

**数据验证错误**: 必填字段缺失、格式不正确等  
**登录错误**: 账户信息错误、登录失败等  
**系统错误**: 人保录单系统返回的错误  
**网络错误**: 网络连接问题、超时等  
**自动化错误**: Python脚本执行过程中的错误  
**汇率错误**: 汇率获取失败或转换错误（国际业务）  

### 7.2 重试策略

对于不同类型的错误采用不同的重试策略：

**可重试错误**: 网络错误、超时错误、系统临时错误  
**重试次数**: 最多3次重试  
**重试间隔**: 使用指数退避算法（2^n秒）  
**重试条件**: 检查错误类型和重试次数  

**不可重试错误**: 数据验证错误、登录错误、账户权限错误  
**直接失败**: 立即返回错误信息  
**错误记录**: 详细记录错误原因和上下文  

### 7.3 错误记录和分析

系统详细记录每次处理的错误信息：
- **错误类型**: 分类记录不同类型的错误
- **错误上下文**: 记录发生错误时的系统状态
- **重试历史**: 记录重试次数和每次重试的结果
- **最终结果**: 记录最终的处理结果和耗时

## 8. 监控和性能

### 8.1 关键指标监控

**录单成功率**: 按模式和账户统计的录单成功率  
**处理时间**: 录单处理的平均时间和分布情况  
**错误分布**: 各类错误的发生频率和趋势分析  
**汇率准确性**: 国际业务中汇率获取和转换的准确性  
**重试效果**: 重试机制的有效性分析  

### 8.2 性能优化

**并发控制**: 合理控制同时进行的录单数量  
**资源管理**: 及时释放浏览器和网络资源  
**缓存机制**: 缓存汇率、配置等常用数据  
**连接复用**: 复用网络连接以提高效率  

### 8.3 日志记录

系统记录详细的处理日志：
- **开始处理**: 订单号、模式、账户、币种等基本信息
- **国际业务**: 币种、汇率、转换金额等特殊信息
- **自动化执行**: 脚本执行的关键步骤和状态
- **处理完成**: 最终结果、处理时间、重试次数等

## 9. Go重构建议

### 9.1 架构设计

**适配器接口**: 定义统一的保险公司适配器接口  
**模式处理器**: 为国内和国际业务设计专用处理器  
**汇率服务**: 独立的汇率获取和转换服务  
**脚本管理器**: 安全可靠的Python脚本执行管理  
**配置管理器**: 动态加载和管理账户配置  

### 9.2 核心组件

**汇率API客户端**: 集成第三方汇率API服务  
**币种转换器**: 实现多币种之间的转换计算  
**时间调整器**: 实现智能的时间调整算法  
**数据验证器**: 验证保单数据的完整性和正确性  
**脚本执行器**: 安全执行Python脚本并处理结果  

### 9.3 技术选型

**HTTP客户端**: net/http包配置超时和重试  
**时间处理**: time包处理时间格式转换  
**数值计算**: math包处理汇率转换计算  
**进程管理**: os/exec包执行Python脚本  
**配置管理**: Viper管理账户配置和业务规则  
**监控指标**: Prometheus客户端库收集指标  

这种设计能够有效支持人保财险的国内外业务需求，同时提供良好的可扩展性和可维护性。
