# 保代云服 (BDYF) 业务逻辑说明

## 1. 基本信息

**保险公司**: 保代云服平台  
**适配器类型**: 自动化录单 (AUTO)  
**支持模式**: AUTO_BDYF  
**数据格式**: 网页表单提交  
**特殊性质**: 保险代理平台，非直接保险公司  

## 2. 业务流程概述

### 2.1 平台特性

保代云服是一个保险代理平台，具有以下特点：

**代理平台**: 连接多家保险公司，提供统一的录单接口  
**统一接口**: 后端对接不同保险公司，前端提供统一操作界面  
**产品丰富**: 支持多种保险产品和条款选择  
**实时处理**: 支持实时录单和即时出单  
**权限管理**: 不同账户有不同的产品权限和费率折扣  

### 2.2 录单处理流程

保代云服的录单流程包括：

1. **平台账户验证**: 验证账户权限和产品访问权限
2. **产品配置加载**: 根据账户加载可用的产品配置
3. **数据标准化处理**: 将平台数据转换为保代云服格式
4. **权限检查**: 检查账户对特定产品的操作权限
5. **自动化录单执行**: 通过Python脚本在保代云服平台录单
6. **后端保险公司处理**: 保代云服将请求转发给后端保险公司
7. **结果获取**: 获取最终的保单信息和承保结果

### 2.3 平台优势

**一站式服务**: 通过一个平台接入多家保险公司  
**统一标准**: 统一的数据格式和操作流程  
**灵活配置**: 支持不同账户的个性化配置  
**实时监控**: 提供实时的录单状态和结果反馈  

## 3. 账户配置体系

### 3.1 账户权限管理

保代云服支持多层次的账户权限管理：

**SHTB账户 (上海太保)**:
- 账户ID: SHTB_BDYF_001
- 访问级别: 标准级别
- 产品权限: 货运险
- 费率折扣: 95%

**YRWL账户 (优然物流)**:
- 账户ID: YRWL_BDYF_002
- 访问级别: 高级级别
- 产品权限: 货运险、责任险
- 费率折扣: 90%

### 3.2 权限级别说明

**标准级别**: 基础的产品访问权限和标准费率  
**高级级别**: 更多产品选择和更优惠的费率  
**企业级别**: 定制化的产品配置和专属服务  

### 3.3 产品权限控制

系统严格控制账户的产品访问权限：
- 检查账户是否有特定产品的操作权限
- 验证产品类型是否在账户的权限范围内
- 应用账户对应的费率折扣
- 记录权限检查的结果和异常情况

## 4. 数据标准化处理

### 4.1 订单号格式化

保代云服要求特定的订单号格式：

**格式要求**: BDYF + 日期(YYYYMMDD) + 序号  
**生成规则**: 如果原订单号已符合格式则直接使用，否则重新生成  
**序号生成**: 使用原订单号的MD5哈希值前6位作为序号  
**唯一性**: 确保在同一天内订单号的唯一性  

### 4.2 货物名称标准化

系统对货物名称进行标准化映射：

**电子产品** → 电子设备及配件  
**服装** → 纺织品及服装  
**食品** → 食品及农产品  
**化工产品** → 化学工业产品  
**机械设备** → 机械设备及零件  
**汽车配件** → 汽车零部件  
**建材** → 建筑材料  
**日用品** → 日用消费品  

如果没有匹配的映射，则保持原货物名称不变。

### 4.3 地点标准化

对运输地点进行标准化处理：

**后缀移除**: 移除"市"、"区"、"县"、"镇"、"港"、"机场"、"站"等常见后缀  
**城市映射**: 将简称映射为全称（如"北京" → "北京市"）  
**格式统一**: 确保地点名称的格式一致性  
**验证检查**: 验证地点名称的有效性  

## 5. 权限管理机制

### 5.1 产品权限检查

系统在处理每个请求时都会进行权限检查：

1. **账户验证**: 确认账户存在且状态正常
2. **产品权限**: 检查账户是否有特定产品的操作权限
3. **访问级别**: 验证账户的访问级别是否满足要求
4. **费率权限**: 确认账户可以使用的费率折扣
5. **异常处理**: 处理权限不足的情况

### 5.2 权限异常处理

当权限检查失败时：
- **权限不足**: 返回明确的权限错误信息
- **账户异常**: 检查账户状态和配置
- **产品限制**: 提示可用的产品类型
- **升级建议**: 提供账户升级的建议

### 5.3 权限日志记录

系统详细记录权限相关的操作：
- **权限检查**: 记录每次权限检查的结果
- **访问尝试**: 记录未授权的访问尝试
- **权限变更**: 记录账户权限的变更历史
- **异常情况**: 记录权限相关的异常和错误

## 6. 自动化录单实现

### 6.1 录单参数准备

系统为自动化脚本准备以下参数：

**平台信息**: 保代云服平台标识和版本信息  
**账户信息**: 账户ID、访问级别、权限列表  
**保单数据**: 标准化后的保单信息  
**执行选项**: 自动提交、等待结果、超时时间、截图等  

### 6.2 自动化执行流程

Python脚本执行以下自动化操作：

1. **平台登录**: 使用账户信息登录保代云服平台
2. **产品选择**: 根据业务类型选择对应的保险产品
3. **信息录入**: 填写投保人、被保险人、货物、运输等信息
4. **费率计算**: 系统自动计算保费并应用账户折扣
5. **数据确认**: 确认所有录入信息的准确性
6. **提交录单**: 提交到后端保险公司进行处理
7. **状态监控**: 监控录单状态和处理进度
8. **结果获取**: 获取最终的保单信息和承保结果

### 6.3 特殊处理逻辑

**超时设置**: 5分钟的处理超时时间  
**截图功能**: 自动截图记录关键操作步骤  
**状态监控**: 实时监控录单的处理状态  
**异常恢复**: 处理网络中断等异常情况  

## 7. 错误处理机制

### 7.1 错误类型分类

**账户权限错误**: 账户不存在、权限不足、状态异常  
**产品配置错误**: 产品不可用、配置缺失、版本不匹配  
**数据验证错误**: 必填字段缺失、格式错误、数据无效  
**平台错误**: 保代云服平台系统错误或维护  
**后端错误**: 后端保险公司返回的错误  
**网络错误**: 网络连接问题、超时等  
**自动化错误**: Python脚本执行过程中的错误  

### 7.2 错误处理策略

**权限错误**: 检查账户配置，提供权限升级建议  
**平台错误**: 可重试，使用指数退避算法  
**后端错误**: 根据错误类型决定是否重试  
**网络错误**: 自动重试，最多3次  
**数据错误**: 直接返回失败，提供详细错误信息  

### 7.3 后端错误分析

系统会分析后端保险公司返回的错误：
- **系统维护**: 延后重试
- **网络超时**: 增加超时时间后重试
- **业务规则**: 不重试，返回具体错误原因
- **数据格式**: 检查数据转换逻辑

## 8. 监控和性能

### 8.1 关键指标监控

**平台可用性**: 保代云服平台的可用性监控  
**录单成功率**: 按账户和产品类型统计的成功率  
**处理时间**: 录单处理的平均时间和分布  
**权限准确性**: 权限检查的准确性和效率  
**后端性能**: 不同后端保险公司的处理性能  

### 8.2 性能优化

**权限缓存**: 缓存账户权限信息以提高检查效率  
**配置缓存**: 缓存产品配置和映射规则  
**连接复用**: 复用与保代云服平台的连接  
**并发控制**: 合理控制并发录单数量  

### 8.3 日志记录

系统记录详细的处理日志：
- **权限检查**: 账户、访问级别、产品权限检查结果
- **数据标准化**: 订单号格式化、货物名称映射、地点标准化结果
- **自动化执行**: 脚本执行的关键步骤和状态变化
- **后端交互**: 与后端保险公司的交互过程和结果

## 9. Go重构建议

### 9.1 架构设计

**权限管理器**: 管理账户权限和产品访问控制  
**数据标准化器**: 实现各种数据的标准化转换  
**平台适配器**: 适配保代云服平台的特殊要求  
**后端代理**: 处理与后端保险公司的交互  
**监控组件**: 实时监控平台状态和性能指标  

### 9.2 核心组件

**权限验证**: 实现基于角色的访问控制(RBAC)  
**数据映射**: 可配置的数据映射和转换规则  
**缓存管理**: 权限、配置等数据的缓存机制  
**脚本管理**: 安全执行Python自动化脚本  
**错误处理**: 统一的错误分类和处理机制  

### 9.3 技术选型

**权限管理**: casbin实现权限控制  
**缓存系统**: Redis缓存权限和配置数据  
**配置管理**: Viper管理账户配置和映射规则  
**HTTP客户端**: net/http包处理平台API调用  
**监控指标**: Prometheus收集性能指标  
**日志记录**: Zap记录结构化日志  

这种设计能够有效支持保代云服平台的特殊要求，同时提供良好的权限管理和性能监控能力。
