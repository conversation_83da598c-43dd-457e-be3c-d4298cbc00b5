# 华泰财险 (HUATAI) 业务逻辑说明

## 1. 基本信息

**保险公司**: 华泰财产保险股份有限公司  
**适配器类型**: API接口 (API)  
**支持模式**: API_HUATAI  
**数据格式**: XML  
**接口类型**: HTTP POST  

## 2. 业务流程概述

### 2.1 API调用流程

华泰财险采用XML格式的HTTP API接口进行保单录入，整个流程包括：

1. **数据接收**: 接收标准化的保单数据
2. **映射转换**: 将平台数据映射为华泰系统格式
3. **XML构建**: 构建符合华泰API规范的XML请求
4. **API调用**: 发送HTTP POST请求到华泰接口
5. **响应解析**: 解析XML响应获取录单结果
6. **错误处理**: 处理各种异常情况和重试机制

### 2.2 接口特点

**XML数据交换**: 使用标准XML格式进行数据传输  
**同步调用**: 实时发送请求并等待响应  
**映射转换**: 需要将标准数据映射为华泰特定格式  
**固定配置**: 使用预定义的固定配置值  

## 3. 数据映射规则

### 3.1 货物类型映射

华泰系统要求将货物名称映射为特定的代码和描述：

**纺织原料及纺织制品**: 代码SX001411, 描述SX00140065  
**机器设备及其零件、附件**: 代码SX001416, 描述SX00140087  
**食品**: 代码SX001404, 描述SX00140019  
**化学工业及其相关工业产品**: 代码SX001406, 描述SX00140040  
**塑料及其制品;橡胶及其制品**: 代码SX001407, 描述SX00140041  
**玻璃及玻璃制品**: 代码SX001413, 描述SX00140072  
**新车/二手车**: 代码SX001417, 描述SX00140089  

系统使用模糊匹配算法，如果货物名称包含上述关键词，则使用对应的映射；否则默认使用食品类别。

### 3.2 运输方式映射

根据运输方式和车型的组合进行映射：

**公路运输(代码3)**:
- 厢式货车: 运输代码SX001501, 车型代码01
- 非厢式货车: 运输代码SX001501, 车型代码05

**铁路运输(代码4)**:
- 厢式货车: 运输代码SX001503, 车型代码01
- 非厢式货车: 运输代码SX001503, 车型代码02

**水路运输(代码5)**:
- 厢式货车: 运输代码SX001502, 车型代码01
- 非厢式货车: 运输代码SX001502, 车型代码02

**航空运输(代码6)**:
- 厢式货车: 运输代码SX001505, 车型代码01
- 非厢式货车: 运输代码SX001505, 车型代码02

如果没有指定车型，系统默认使用厢式货车。

### 3.3 条款映射

保险条款的映射相对简单：
- **JBX (基本险)**: 华泰条款代码SX300211
- **ZHX (综合险)**: 华泰条款代码SX300212

## 4. 固定配置管理

### 4.1 系统固定值

华泰系统需要一些固定的配置值：

**查勘地址ID**: 501422495713  
**查勘地址**: 17/F,Block B,Center Plaza 161 Linhexi Av.,Tianhe District, Guangzhou TEL:4006095509 FAX: 020-87567201  
**起运国家**: HTC01  
**到达国家**: HTC01  
**证件类型**: 99  

这些固定值在所有请求中都保持不变，确保与华泰系统的兼容性。

### 4.2 配置值说明

**查勘地址**: 用于指定事故查勘的联系地址和电话  
**国家代码**: HTC01代表中国，适用于国内运输业务  
**证件类型**: 99是华泰系统中的通用证件类型代码  

## 5. XML数据结构

### 5.1 请求XML结构

华泰API要求的XML请求包含以下主要部分：

**头部信息**: 版本号、时间戳、请求ID等元数据  
**保单信息**: 订单号、投保人信息、被保险人信息  
**货物信息**: 货物名称、类型代码、保险金额  
**运输信息**: 运输方式、车型、起止地点、出发时间  
**条款信息**: 保险条款代码和名称  
**查勘信息**: 查勘地址ID和详细地址  
**保费信息**: 保险费金额  

### 5.2 响应XML结构

华泰系统返回的XML响应包含：

**头部信息**: 结果代码、结果消息、时间戳  
**保单结果**: 成功标志、保单号、申请号  
**错误信息**: 错误代码和错误消息（如有）  

## 6. 业务逻辑实现

### 6.1 数据转换处理

系统接收标准化的保单数据后，进行以下转换：

1. **基础信息提取**: 订单号、投保人信息、被保险人信息等
2. **货物信息映射**: 根据货物名称查找对应的华泰代码和描述
3. **运输信息映射**: 根据运输方式和车型确定华泰的运输代码
4. **条款信息映射**: 将平台条款类型转换为华泰条款代码
5. **固定值填充**: 添加华泰系统要求的固定配置值
6. **时间格式化**: 将时间戳转换为华泰要求的日期时间格式

### 6.2 XML构建过程

使用SimpleXMLElement类构建XML请求：

1. **创建根元素**: 创建request根元素
2. **添加头部**: 版本、时间戳、请求ID等
3. **构建主体**: 添加policy_info主要内容
4. **填充数据**: 按照华泰API规范填充各个字段
5. **特殊字符处理**: 对文本内容进行HTML转义
6. **生成XML**: 输出格式化的XML字符串

### 6.3 API调用处理

HTTP请求的处理包括：

1. **请求配置**: 设置Content-Type为application/xml
2. **超时设置**: 30秒的请求超时时间
3. **错误处理**: 捕获网络连接错误
4. **响应验证**: 检查HTTP状态码和响应内容
5. **XML解析**: 解析返回的XML响应数据

## 7. 错误处理机制

### 7.1 错误类型分类

**货物类型映射失败**: 无法找到匹配的货物类型  
**运输方式映射失败**: 运输方式或车型无效  
**XML构建失败**: XML格式错误或数据无效  
**API调用失败**: 网络连接问题或HTTP错误  
**XML解析失败**: 响应格式错误或内容无效  
**华泰系统错误**: 华泰系统返回的业务错误  

### 7.2 重试策略

对于可重试的错误类型：
- **网络连接超时**: 自动重试，最多3次
- **华泰系统错误**: 根据错误类型决定是否重试
- **临时性错误**: 使用指数退避算法延后重试

对于不可重试的错误：
- **数据格式错误**: 直接返回失败
- **映射失败**: 使用默认值或返回错误
- **XML格式错误**: 检查数据完整性后返回错误

### 7.3 错误记录和监控

系统详细记录每次API调用的情况：
- **请求信息**: 订单号、请求时间、请求参数
- **映射结果**: 货物类型、运输方式的映射结果
- **API调用**: 请求URL、响应时间、状态码
- **处理结果**: 成功状态、保单号、错误信息

## 8. 性能和监控

### 8.1 性能优化

**连接复用**: 使用HTTP连接池减少连接开销  
**并发控制**: 限制同时进行的API调用数量  
**缓存机制**: 缓存映射表和配置信息  
**超时管理**: 合理设置各种超时时间  

### 8.2 监控指标

**API成功率**: 华泰API调用的成功率统计  
**响应时间**: API调用的平均响应时间  
**错误分布**: 各类错误的发生频率和趋势  
**映射准确性**: 货物类型和运输方式映射的准确性  

## 9. Go重构建议

### 9.1 架构设计

**适配器接口**: 定义统一的API适配器接口  
**映射管理器**: 管理各种数据映射规则  
**XML处理器**: 专门处理XML构建和解析  
**HTTP客户端**: 可配置的HTTP客户端管理  
**错误处理器**: 统一的错误分类和处理机制  

### 9.2 核心组件

**映射表管理**: 使用配置文件管理货物类型、运输方式等映射表  
**XML模板**: 使用Go的XML标签定义请求和响应结构  
**HTTP客户端池**: 管理HTTP连接的复用和超时  
**重试机制**: 实现可配置的重试策略  
**监控集成**: 集成Prometheus等监控系统  

### 9.3 技术选型

**XML处理**: encoding/xml包处理XML序列化和反序列化  
**HTTP客户端**: net/http包配置超时和重试  
**配置管理**: Viper管理映射表和固定配置  
**日志记录**: Zap记录结构化日志  
**监控指标**: Prometheus客户端库  

这种设计能够提供高性能、可靠的API调用能力，同时保持良好的可维护性和可扩展性。
