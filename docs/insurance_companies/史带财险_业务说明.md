# 史带财险 (STARR) 业务逻辑说明

## 1. 基本信息

**保险公司**: 史带财产保险股份有限公司  
**适配器类型**: API接口 (API)  
**支持模式**: API_STARR  
**数据格式**: JSON  
**特殊处理**: 需要生成GUID和MD5签名  

## 2. 业务流程概述

### 2.1 API调用流程

史带财险采用JSON格式的API接口，具有以下特点：

1. **身份类型识别**: 自动识别投保人是企业还是个人
2. **固定配置加载**: 加载史带系统要求的固定配置参数
3. **GUID生成**: 为每个请求生成唯一的GUID标识
4. **数据签名**: 对关键数据进行MD5签名验证
5. **JSON数据构建**: 构建符合史带API规范的JSON请求
6. **API请求发送**: 发送HTTPS POST请求到史带接口
7. **响应解析**: 解析JSON响应并提取保单信息

### 2.2 接口特点

**JSON数据交换**: 使用JSON而非XML进行数据传输  
**GUID标识**: 每个请求需要唯一的GUID  
**MD5签名**: 对关键数据进行签名验证  
**身份识别**: 区分企业和个人的证件类型  
**HTTPS安全**: 使用HTTPS确保数据传输安全  

## 3. 固定配置管理

### 3.1 系统固定配置

史带财险系统需要以下固定配置参数：

**合作伙伴代码**: YOUFU  
**合作伙伴密钥**: km2a0f13c9dkb8 (用于签名)  
**产品代码**: 60021  
**操作类型**: 006 (投保操作)  
**支付处理方式**: 2  
**保险期间类型**: M (月)  
**保险期间**: 1 (1个月)  
**货物运输方式**: 3  
**通知邮箱**: <EMAIL>  

### 3.2 身份处理配置

**企业参与方类型**: QY  
**企业证件类型**: 104 (营业执照)  
**个人参与方类型**: GR  
**个人证件类型**: 1 (身份证)  

这些配置确保了与史带系统的正确对接和数据格式的一致性。

## 4. 身份识别机制

### 4.1 证件类型识别

系统自动识别投保人的身份类型：

**营业执照识别**: 18位数字字母组合，格式为[0-9A-Z]{18}  
**身份证识别**: 15位纯数字或18位数字加X的组合  
**默认处理**: 无法识别的格式默认按个人身份证处理  

### 4.2 身份类型映射

根据识别结果设置对应的参与方类型和证件类型：

**企业身份**:
- 参与方类型: QY
- 证件类型: 104
- 适用于营业执照号码

**个人身份**:
- 参与方类型: GR
- 证件类型: 1
- 适用于身份证号码

### 4.3 识别算法

系统使用正则表达式进行证件类型识别：
1. 首先检查是否为18位营业执照格式
2. 然后检查是否为15位或18位身份证格式
3. 无法识别的默认为个人身份证
4. 根据识别结果设置对应的类型代码

## 5. GUID和签名机制

### 5.1 GUID生成

每个API请求都需要生成唯一的GUID：

**格式**: 标准UUID格式，如550e8400-e29b-41d4-a716-************  
**版本**: UUID版本4 (随机生成)  
**用途**: 作为请求的唯一标识符  
**生成**: 使用加密安全的随机数生成器  

### 5.2 MD5签名生成

对关键数据进行MD5签名验证：

**签名字符串构成**:
1. 合作伙伴代码
2. 订单号
3. 保险金额
4. 保险费
5. 投保人姓名
6. 投保人证件号
7. 合作伙伴密钥 (放在最后)

**签名过程**:
1. 按顺序连接上述字段
2. 对连接后的字符串计算MD5哈希
3. 将哈希值作为签名包含在请求中

### 5.3 签名验证

史带系统会验证请求中的签名：
- 使用相同的算法重新计算签名
- 比较计算结果与请求中的签名
- 签名不匹配则拒绝请求
- 确保数据传输的完整性和安全性

## 6. JSON数据结构

### 6.1 请求JSON结构

史带API要求的JSON请求包含：

**头部信息**: 合作伙伴代码、请求ID(GUID)、时间戳、签名  
**保单信息**: 订单号、产品代码、操作类型  
**投保人信息**: 姓名、证件号、参与方类型、证件类型  
**被保险人信息**: 被保险人姓名  
**货物信息**: 货物名称、保险金额、保险费  
**运输信息**: 起止地点、经停地、出发时间、运输方式  
**保险信息**: 保险期间类型、期间、支付方式  
**联系信息**: 通知邮箱  

### 6.2 响应JSON结构

史带系统返回的JSON响应包含：

**头部信息**: 结果代码、结果消息、时间戳、响应ID  
**保单结果**: 成功标志、保单号、申请号、承保状态、生效时间、到期时间、错误信息  

## 7. 业务逻辑实现

### 7.1 数据转换处理

系统对传入数据进行以下处理：

1. **固定配置填充**: 添加史带系统要求的固定参数
2. **GUID生成**: 为请求生成唯一标识符
3. **身份识别**: 自动识别投保人身份类型
4. **基础信息处理**: 订单号、投保人信息、被保险人信息
5. **货物信息处理**: 货物名称、保险金额、保险费
6. **运输信息处理**: 起止地点、出发时间等
7. **时间戳添加**: 添加当前时间戳
8. **签名生成**: 对关键数据生成MD5签名

### 7.2 JSON构建过程

构建JSON请求的过程：

1. **创建数据结构**: 定义嵌套的数组和对象结构
2. **填充头部**: 合作伙伴信息、GUID、时间戳、签名
3. **构建主体**: 保单信息的各个部分
4. **数据验证**: 确保必填字段完整
5. **格式化输出**: 生成格式化的JSON字符串

### 7.3 API调用处理

HTTPS请求的处理：

1. **请求配置**: 设置Content-Type为application/json
2. **认证头**: 添加必要的认证信息
3. **SSL配置**: 配置SSL证书验证
4. **超时设置**: 30秒的请求超时
5. **错误处理**: 捕获网络和HTTP错误
6. **响应验证**: 验证响应格式和内容

## 8. 错误处理机制

### 8.1 错误类型分类

**GUID生成失败**: 随机数生成器错误  
**MD5签名生成失败**: 签名算法执行错误  
**身份类型识别失败**: 证件号格式无法识别  
**JSON构建失败**: 数据结构错误  
**API调用失败**: 网络连接或HTTP错误  
**JSON解析失败**: 响应格式错误  
**签名验证失败**: 史带系统拒绝签名  
**网络连接超时**: 请求超时错误  

### 8.2 重试策略

对于可重试的错误：
- **网络连接错误**: 自动重试，最多3次
- **史带系统错误**: 根据错误类型决定重试
- **超时错误**: 增加超时时间后重试

对于不可重试的错误：
- **签名验证失败**: 检查密钥配置
- **数据格式错误**: 直接返回失败
- **身份识别失败**: 使用默认配置

### 8.3 签名验证处理

当签名验证失败时：
1. 检查合作伙伴密钥配置
2. 验证签名字符串的构建顺序
3. 确认数据字段的完整性
4. 重新生成签名并重试

## 9. 监控和日志

### 9.1 关键指标监控

**API成功率**: 史带API调用的成功率  
**签名验证成功率**: MD5签名验证的成功率  
**身份识别准确性**: 证件类型识别的准确性  
**响应时间**: API调用的平均响应时间  
**GUID唯一性**: 确保GUID的唯一性  

### 9.2 日志记录

系统记录以下关键信息：
- **GUID生成**: 每个请求的GUID和生成时间
- **身份识别**: 证件类型识别结果
- **签名生成**: 签名字符串和MD5值
- **API调用**: 请求参数、响应时间、结果状态
- **错误处理**: 错误类型、重试次数、最终结果

## 10. Go重构建议

### 10.1 架构设计

**身份识别器**: 自动识别证件类型和身份类别  
**GUID生成器**: 生成唯一的请求标识符  
**签名生成器**: 实现MD5签名算法  
**JSON处理器**: 处理JSON构建和解析  
**HTTP客户端**: 支持HTTPS和超时配置  

### 10.2 核心组件

**配置管理**: 管理固定配置和合作伙伴信息  
**身份验证**: 实现证件类型的正则表达式匹配  
**加密服务**: 提供GUID生成和MD5签名功能  
**HTTP客户端池**: 管理HTTPS连接的复用  
**错误处理**: 统一的错误分类和重试机制  

### 10.3 技术选型

**UUID生成**: github.com/google/uuid包  
**MD5哈希**: crypto/md5包  
**JSON处理**: encoding/json包  
**HTTP客户端**: net/http包配置HTTPS  
**正则表达式**: regexp包处理证件识别  
**配置管理**: Viper管理固定配置  

这种设计能够提供安全、可靠的API调用能力，同时确保数据传输的完整性和安全性。
