# 太保财险 (CPIC) 业务逻辑说明

## 1. 基本信息

**保险公司**: 中国太平洋财产保险股份有限公司  
**适配器类型**: 自动化录单 (AUTO)  
**支持模式**: AUTO_CPIC, AUTO_CPIC_INTL  
**数据格式**: 网页表单提交  

## 2. 业务流程概述

### 2.1 录单处理流程

太保财险采用自动化网页操作方式，通过Python脚本在太保录单系统中完成保单录入。主要流程包括：

1. **模式判断**: 区分国内业务(AUTO_CPIC)和国际业务(AUTO_CPIC_INTL)
2. **账户配置**: 根据账户参数加载对应的配置信息和特殊处理规则
3. **数据处理**: 将标准化数据转换为太保系统格式，处理特殊字段
4. **自动化录单**: Python脚本自动填写太保录单系统表单
5. **结果获取**: 获取录单结果和保单号信息

### 2.2 业务模式差异

**AUTO_CPIC (国内业务)**:
- 处理国内货物运输保险
- 使用标准的数据格式和处理流程
- 支持常规的货物类型和运输条款

**AUTO_CPIC_INTL (国际业务)**:
- 处理国际货物运输保险
- 需要处理国际贸易相关的特殊条款
- 可能涉及外币计价和特殊运输条件

## 3. 账户配置体系

### 3.1 主要账户配置

太保财险支持多个账户，每个账户都有特定的配置参数：

**SHTB账户 (上海太保)**:
- 协议号: A|3010200|C20180144-19001P000431|CAGHYX190032
- 费率信息: ["0.5", "3000000", "50"]
- 适用范围: 标准货运保险业务

**YRWL账户 (优然物流)**:
- 协议号: A|3010200|C20180144-19001P000431|CAGHYX190032
- 费率信息: ["0.5", "3000000", "50"]
- 适用范围: 物流行业专用账户

**CDYF账户 (成都优孚)**:
- 协议号: A|3010200|C20180144-19001P000431|CAGHYX190032
- 费率信息: ["0.5", "3000000", "50"]
- 特殊处理: 支持货物覆盖范围的JSON数据

### 3.2 CDYF账户特殊处理

CDYF账户具有特殊的货物覆盖范围处理逻辑：
- 当传入cargo_coverage_json参数时，系统会解析JSON数据
- 提取货物覆盖范围信息并格式化为太保系统要求的格式
- 这个功能主要用于处理复杂的货物保障范围定义

## 4. 数据处理逻辑

### 4.1 基础数据处理

系统对传入的保单数据进行标准化处理：

**订单信息**: 订单号、货物名称、保险金额、保费等基础信息  
**投保人信息**: 投保人姓名、证件号码、联系方式等  
**被保险人信息**: 被保险人姓名和相关信息  
**运输信息**: 起运地、目的地、经停地、运输方式、出发时间等  

### 4.2 账户配置映射

根据传入的账户参数，系统会：
1. 查找对应的账户配置信息
2. 获取协议号、费率信息等固定参数
3. 应用账户特有的处理规则
4. 对于CDYF账户，还会处理货物覆盖范围的特殊逻辑

### 4.3 数据格式化

系统将处理后的数据格式化为太保录单系统要求的格式：
- 金额字段保留两位小数
- 时间字段转换为指定格式
- 文本字段进行必要的编码处理
- 特殊字段按照太保系统要求进行格式化

## 5. 特殊业务规则

### 5.1 货物覆盖范围处理

对于CDYF账户，系统支持复杂的货物覆盖范围定义：
- 接收JSON格式的货物覆盖范围数据
- 解析并验证JSON数据的有效性
- 将覆盖范围信息转换为太保系统识别的格式
- 在录单时将这些信息正确填入对应字段

### 5.2 协议号管理

每个账户都有固定的协议号格式：
- 协议号包含多个部分，用"|"分隔
- 不同部分代表不同的业务含义
- 系统根据账户自动选择正确的协议号

### 5.3 费率信息处理

费率信息以数组形式存储，包含：
- 基础费率: 通常为"0.5"（0.5%）
- 最高保额: 如"3000000"（300万）
- 其他费率参数: 根据业务需要设置

## 6. 自动化录单实现

### 6.1 Python脚本调用

系统通过以下方式调用Python自动化脚本：
- 传递模式参数（AUTO_CPIC或AUTO_CPIC_INTL）
- 传递账户配置信息
- 传递处理后的保单数据
- 设置适当的超时时间和重试参数

### 6.2 录单流程

Python脚本执行以下自动化操作：
1. 启动浏览器并访问太保录单系统
2. 使用账户信息登录系统
3. 选择对应的保险产品和业务类型
4. 按顺序填写各个表单字段
5. 上传必要的附件（如有）
6. 提交录单申请
7. 等待系统处理并获取结果
8. 返回保单号或错误信息

### 6.3 异常处理

自动化过程中可能遇到的异常：
- 网络连接问题：重试机制
- 页面加载超时：延长等待时间
- 表单验证失败：检查数据格式
- 系统维护：延后处理
- 登录失败：检查账户状态

## 7. 错误处理机制

### 7.1 错误分类

**数据验证错误**: 必填字段缺失、格式不正确等  
**账户配置错误**: 账户不存在、配置信息错误等  
**网络连接错误**: 网络超时、连接失败等（可重试）  
**系统错误**: 太保录单系统返回的错误  
**自动化错误**: Python脚本执行过程中的错误  

### 7.2 重试策略

对于可重试的错误：
- 网络连接问题：最多重试3次
- 系统临时错误：延后重试
- 超时错误：增加超时时间后重试
- 使用指数退避算法控制重试间隔

### 7.3 日志记录

系统详细记录处理过程：
- 开始处理：记录订单号、模式、账户等基本信息
- 配置加载：记录使用的账户配置信息
- 数据处理：记录关键的数据转换过程
- 自动化执行：记录脚本执行的关键步骤
- 处理完成：记录最终结果、处理时间、重试次数等

## 8. 监控和性能

### 8.1 关键指标

**成功率监控**: 按账户和模式统计录单成功率  
**处理时间**: 监控平均处理时间和异常情况  
**错误分析**: 统计各类错误的发生频率和趋势  
**账户性能**: 监控不同账户的处理性能差异  

### 8.2 性能优化

**并发控制**: 合理控制并发录单数量，避免对太保系统造成压力  
**缓存机制**: 缓存账户配置信息，减少重复加载  
**连接池**: 复用网络连接，提高处理效率  
**资源管理**: 及时释放浏览器资源，避免内存泄漏  

## 9. Go重构建议

### 9.1 架构设计

**适配器模式**: 实现统一的保险公司适配器接口  
**配置管理**: 使用结构化配置文件管理账户信息  
**模式处理**: 为不同业务模式设计专用处理器  
**自动化管理**: 安全可靠的Python脚本执行管理  

### 9.2 核心组件

**配置管理器**: 动态加载和管理账户配置，支持热更新  
**数据处理器**: 标准化的数据转换和格式化处理  
**脚本管理器**: 安全执行Python脚本，支持超时和重试  
**错误处理器**: 统一的错误分类、重试和日志记录  
**监控组件**: 实时监控处理状态和性能指标  

### 9.3 技术选型

**Web框架**: Gin - 高性能的HTTP框架  
**配置管理**: Viper - 灵活的配置管理库  
**日志记录**: Zap - 高性能结构化日志  
**并发控制**: Context - 超时和取消控制  
**监控指标**: Prometheus - 指标收集和监控  

这种设计能够保持业务逻辑的清晰性，同时提供良好的可扩展性、可维护性和可观测性。
