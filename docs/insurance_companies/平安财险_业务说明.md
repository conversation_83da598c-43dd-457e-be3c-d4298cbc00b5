# 平安财险 (PINGAN) 业务逻辑说明

## 1. 基本信息

**保险公司**: 中国平安财产保险股份有限公司  
**适配器类型**: 自动化录单 (AUTO)  
**支持模式**: AUTO_PINGAN, AUTO_PINGAN_INTL, AUTO_PINGAN_CBEC  
**数据格式**: 网页表单提交  

## 2. 业务流程概述

### 2.1 录单处理流程

平安财险采用自动化网页操作方式，通过Python脚本模拟用户在平安录单系统中的操作。整个流程包括：

1. **模式识别**: 根据传入参数判断是国内业务、国际业务还是跨境电商业务
2. **账户配置**: 根据不同账户加载对应的投保人信息和处理规则
3. **数据验证**: 对身份信息进行严格验证，特别是身份证号码的格式和校验码
4. **信息处理**: 将标准化数据转换为平安系统要求的格式
5. **自动化录单**: 通过Python脚本自动填写表单并提交
6. **结果处理**: 获取录单结果，成功返回保单号，失败进行重试

### 2.2 三种业务模式

**AUTO_PINGAN (国内货运险)**:
- 标准的国内货物运输保险业务
- 使用标准处理流程和10分钟超时设置
- 支持常规货物类型和运输方式

**AUTO_PINGAN_INTL (国际货运险)**:
- 针对国际货物运输的保险业务
- 处理复杂的国际贸易条款
- 可能涉及外币和汇率转换

**AUTO_PINGAN_CBEC (跨境电商)**:
- 专门为跨境电商设计的保险产品
- 特殊的2小时失败检测超时时间
- 处理流程复杂，需要更长处理时间

## 3. 账户配置管理

### 3.1 账户体系

平安财险支持多个账户配置，每个账户都有固定的投保人信息：

**SHTB账户 (上海太保)**:
- 投保人: 上海太保货运代理有限公司
- 营业执照: 91310115MA1FL6LQ5X
- 适用: 标准货运保险业务

**YRWL账户 (优然物流)**:
- 投保人: 上海优然物流有限公司
- 营业执照: 91310115MA1FL6LQ5X
- 适用: 物流行业专用

**CDYF账户 (成都优孚)**:
- 投保人: 成都优孚世纪信息技术有限公司
- 营业执照: 91510100MA61R8LN8H
- 适用: 西南地区业务

### 3.2 账户选择逻辑

系统根据传入的账户参数自动选择对应配置。如果账户配置中包含固定投保人信息，则使用配置中的信息；否则使用传入的投保人数据。

## 4. 数据处理逻辑

### 4.1 投保人信息处理

系统首先检查账户配置是否包含固定的投保人信息。如果有，则使用配置中的固定信息；如果没有，则使用传入的投保人数据，并进行身份验证。

### 4.2 身份证验证机制

对于个人投保人，系统实现了完整的身份证号码验证：

**18位身份证验证**:
- 格式检查：必须是17位数字加1位数字或X
- 校验码验证：使用标准算法验证最后一位校验码
- 支持因子数组：[7,9,10,5,8,4,2,1,6,3,7,9,10,5,8,4,2]
- 验证码对应：['1','0','X','9','8','7','6','5','4','3','2']

**15位身份证验证**:
- 格式检查：必须是15位纯数字
- 兼容老式身份证格式

### 4.3 证件类型识别

系统自动识别证件类型：
- 18位或15位数字：身份证
- 18位数字字母组合：营业执照
- 其他格式：护照或其他证件

### 4.4 发票号格式化

根据不同平台有不同的发票号格式：
- 保呀平台(ID=5)：发票号 + " / " + 运单号
- 其他平台：订单号 + " / " + 发票号

## 5. 特殊处理规则

### 5.1 超时设置

不同模式有不同的失败检测超时时间：
- AUTO_PINGAN: 10分钟
- AUTO_PINGAN_INTL: 10分钟
- AUTO_PINGAN_CBEC: 2小时（跨境电商需要更长处理时间）

### 5.2 时间调整

系统对出发时间进行智能调整：
- 当天出发：延后1小时
- 当前分钟数超过50：再延后1小时
- 避免24点后的时间：改为次日00:00

### 5.3 数据准备

客户数据准备包括：
- 基础信息：订单号、货物名称、保额、保费
- 投保人信息：根据账户配置决定使用固定信息还是传入信息
- 被保险人信息：直接使用传入数据
- 运输信息：起运地、目的地、经停地、运输方式
- 时间信息：调整后的出发时间
- 发票信息：格式化的发票号

## 6. 错误处理机制

### 6.1 错误分类

系统将错误分为以下类型：
- **数据验证错误**: 身份证格式错误、必填字段缺失等
- **账户配置错误**: 账户不存在、配置信息错误等
- **网络连接错误**: 网络超时、连接失败等（可重试）
- **自动化脚本错误**: Python脚本执行失败等
- **平安系统错误**: 平安录单系统返回的错误

### 6.2 重试策略

对于可重试的错误，系统采用指数退避策略：
- 最大重试次数：3次
- 重试间隔：2^n秒（n为重试次数）
- 可重试错误：网络错误、超时错误、系统临时错误

### 6.3 错误记录

系统详细记录每次处理的关键信息：
- 开始处理：订单号、模式、账户、投保人类型
- 身份验证：证件类型、验证结果
- 处理完成：成功状态、保单号、处理时间、重试次数

## 7. 自动化实现

### 7.1 Python脚本调用

系统通过shell命令调用Python自动化脚本，传递以下参数：
- 模式：AUTO_PINGAN/AUTO_PINGAN_INTL/AUTO_PINGAN_CBEC
- 账户：SHTB/YRWL/CDYF等
- 平安系统URL：根据模式确定的录单系统地址
- 登录信息：账户对应的登录凭据
- 客户数据：处理后的保单数据
- 超时时间：根据模式设置的超时时间
- 重试次数：默认3次

### 7.2 自动化流程

Python脚本执行以下步骤：
1. 启动浏览器并设置用户代理
2. 登录平安录单系统
3. 选择对应的业务类型
4. 填写投保人、被保险人信息
5. 填写货物和运输信息
6. 系统自动校验数据
7. 提交录单申请
8. 获取录单结果和保单号
9. 处理异常情况和重试

## 8. Go重构建议

### 8.1 架构设计

建议采用以下Go架构：
- **适配器接口**: 定义统一的保险公司适配器接口
- **配置管理**: 使用YAML文件管理账户配置和业务规则
- **身份验证服务**: 独立的身份证验证服务
- **模式处理器**: 不同模式的专用处理器
- **自动化管理器**: 管理Python脚本的执行

### 8.2 关键组件

**身份验证器**: 实现18位和15位身份证验证算法  
**配置管理器**: 动态加载和管理账户配置  
**模式处理器**: 处理不同业务模式的特殊逻辑  
**脚本管理器**: 安全地执行Python自动化脚本  
**错误处理器**: 统一的错误分类和重试机制  

### 8.3 技术栈

- **Web框架**: Gin
- **配置管理**: Viper
- **日志记录**: Zap
- **错误处理**: pkg/errors
- **并发控制**: Context
- **脚本执行**: os/exec

这种架构设计能够保持业务逻辑的清晰性，同时提供良好的可扩展性和维护性。
